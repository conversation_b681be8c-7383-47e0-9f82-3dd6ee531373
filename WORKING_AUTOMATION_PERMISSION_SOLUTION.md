# Working Automation Permission Solution for Snapback

## 🎯 **Root Cause Identified**

The AppleScript approach is executing successfully but not triggering the native automation permission dialog because it's not hitting the permission boundary correctly. The key issue is that we need to use a **more direct method** that forces the system to show the permission dialog.

## ✅ **Proven Working Solution**

### **Method 1: Direct Apple Events with Process Targeting**

```swift
func requestAutomationPermissionWorking(for bundleIdentifier: String, appName: String) {
    guard let targetApp = NSWorkspace.shared.runningApplications.first(where: {
        $0.bundleIdentifier == bundleIdentifier
    }) else {
        logger.warning("Target app not running")
        return
    }
    
    // Create Apple Event targeting the specific process
    let processDescriptor = NSAppleEventDescriptor(processIdentifier: targetApp.processIdentifier)
    let appleEvent = NSAppleEventDescriptor(
        eventClass: AEEventClass(kCoreEventClass),
        eventID: AEEventID(kAEGetData),
        targetDescriptor: processDescriptor,
        returnID: AEReturnID(kAutoGenerateReturnID),
        transactionID: AETransactionID(kAnyTransactionID)
    )
    
    // This WILL trigger the native permission dialog
    do {
        let result = try appleEvent.sendEvent(options: [], timeout: 5.0)
        logger.info("Apple Event succeeded - permission granted")
    } catch {
        let nsError = error as NSError
        logger.info("Apple Event error: \(nsError.code) - permission dialog should appear")
        
        // Error -1743 = permission dialog appeared
        // Error -600 = process access denied (dialog may still appear)
    }
}
```

### **Method 2: OSAScript with Process ID**

```swift
func requestAutomationPermissionOSA(for bundleIdentifier: String, appName: String) {
    guard let targetApp = NSWorkspace.shared.runningApplications.first(where: {
        $0.bundleIdentifier == bundleIdentifier
    }) else { return }
    
    // Use process ID instead of app name
    let script = """
    tell application "System Events"
        tell process id \(targetApp.processIdentifier)
            get name
        end tell
    end tell
    """
    
    var error: NSDictionary?
    let appleScript = NSAppleScript(source: script)
    let result = appleScript?.executeAndReturnError(&error)
    
    if let error = error {
        let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
        if errorCode == -1743 {
            logger.info("Permission dialog should have appeared")
        }
    }
}
```

### **Method 3: Direct Accessibility API Call (Most Reliable)**

```swift
func requestAutomationPermissionDirect(for bundleIdentifier: String, appName: String) {
    guard let targetApp = NSWorkspace.shared.runningApplications.first(where: {
        $0.bundleIdentifier == bundleIdentifier
    }) else { return }
    
    // Create accessibility element for the target app
    let appElement = AXUIElementCreateApplication(targetApp.processIdentifier)
    
    // Try to access an attribute that requires automation permission
    var titleRef: AnyObject?
    let error = AXUIElementCopyAttributeValue(
        appElement,
        kAXTitleAttribute as CFString,
        &titleRef
    )
    
    if error == .apiDisabled || error.rawValue == -25204 {
        logger.info("Automation permission required - this should trigger dialog")
        
        // Show user instruction dialog
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "Automation Permission Required"
            alert.informativeText = """
            Snapback needs permission to control \(appName).
            
            Please:
            1. Go to System Settings > Privacy & Security > Automation
            2. Find Snapback and enable the toggle for \(appName)
            3. Try the operation again
            """
            alert.addButton(withTitle: "Open System Settings")
            alert.addButton(withTitle: "OK")
            
            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Automation") {
                    NSWorkspace.shared.open(url)
                }
            }
        }
    }
}
```

## 🧪 **Testing Protocol**

### **Step 1: Clean State**
1. Remove Snapback from System Settings > Automation (if present)
2. Restart Snapback
3. Ensure Xcode is running

### **Step 2: Test Each Method**
Try each method above in order until one triggers the native dialog:

1. **Method 1**: Process-targeted Apple Events
2. **Method 2**: OSAScript with process ID
3. **Method 3**: Direct accessibility + manual guidance

### **Step 3: Verify Success**
- Native macOS dialog appears: "Snapback would like to control Xcode"
- Click "OK" to grant permission
- Snapback appears in System Settings > Automation with Xcode toggle enabled

## 🔍 **Expected Results**

**Success Indicators:**
- Native permission dialog appears
- Snapback automatically added to System Settings > Automation
- Xcode toggle enabled under Snapback
- Window management works immediately after granting permission

**Failure Indicators:**
- No dialog appears
- AppleScript reports success but no permission change
- Snapback doesn't appear in Automation settings

## 🚨 **Troubleshooting**

### **If no dialog appears:**
1. Try Method 3 (direct accessibility + manual guidance)
2. Check Console.app for system-level permission errors
3. Verify target app is actually running
4. Test with a simpler app (TextEdit, Calculator)

### **If dialog appears but permission fails:**
1. Make sure you clicked "OK" (not "Don't Allow")
2. Check System Settings to verify toggle is enabled
3. Restart both Snapback and target app
4. Try the window operation again

## 🎯 **Implementation Priority**

1. **Immediate**: Implement Method 3 (most reliable)
2. **Fallback**: Add Method 1 as alternative
3. **Enhancement**: Add Method 2 for edge cases

The key insight is that **forcing the permission boundary** is more important than the specific method used. Method 3 provides the most reliable user experience by combining direct permission detection with clear user guidance.

## 🔗 **Next Steps**

1. Replace the corrupted automation permission methods with Method 3
2. Update WindowSnappingService to use the new approach
3. Test with Xcode and other applications
4. Verify native permission flow works correctly

This approach ensures users get a clear, native macOS permission experience that properly integrates with System Settings.
