<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAccessibilityDescription</key>
	<string><PERSON><PERSON><PERSON> needs accessibility access to manage windows.</string>

	<!-- CRITICAL: Automation permission entries for native dialog support -->
	<key>NSAppleEventsUsageDescription</key>
	<string><PERSON><PERSON><PERSON> needs to control other applications to manage their windows and workspaces.</string>

	<!-- Specific automation permissions for common applications -->
	<key>NSAppleScriptEnabled</key>
	<true/>

	<!-- Request automation access for specific applications -->
	<key>NSAutomationUsageDescription</key>
	<string><PERSON><PERSON><PERSON> needs automation access to manage windows and save workspaces across different applications.</string>
</dict>
</plist>
