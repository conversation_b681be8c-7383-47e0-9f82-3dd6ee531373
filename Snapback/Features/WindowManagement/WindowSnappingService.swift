import AppKit
import Carbon

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared

    // MARK: - Dependencies
    private let windowMover: WindowMover
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let accessibilityElement: AccessibilityElement

    // MARK: - Initialization
    init(
        windowMover: WindowMover = WindowMover(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService(),
        accessibilityElement: AccessibilityElement = AccessibilityElement()
    ) {
        self.windowMover = windowMover
        self.calculationService = calculationService
        self.screenDetection = screenDetection
        self.accessibilityElement = accessibilityElement
        logger.info("Initialized with dependencies", service: serviceName)
    }

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        Task {
            do {
                let window = try await getFrontmostWindow()
                try await snapWindow(window, to: position)
            } catch WindowSnappingError.noFrontmostWindow {
                logger.warning("No frontmost window found", service: serviceName)
            } catch WindowSnappingError.noTargetScreen {
                logger.warning("Failed to determine target screen", service: serviceName)
            } catch {
                logger.error("Error snapping window: \(error)", service: serviceName)
            }
        }
    }

    // MARK: - Private Methods
    private func snapWindow(_ window: AXUIElement?, to position: SnapPosition) async throws {
        guard let window = window else {
            logger.warning("Window is nil", service: serviceName)
            throw WindowSnappingError.noFrontmostWindow
        }

        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()

        // Use Rectangle's screen detection approach
        guard let usableScreens = screenDetection.detectScreens(using: window) else {
            logger.warning(
                "Failed to detect screens using Rectangle's approach", service: serviceName)
            throw WindowSnappingError.noTargetScreen
        }

        let currentScreen = usableScreens.currentScreen
        let direction = convertSnapPositionToDirection(position)

        // Log comprehensive screen and window information
        logger.info(
            "Window Management Operation - Position: \(position), Direction: \(direction), "
                + "Current Screen: \(screenIdentifier(currentScreen)), Total Screens: \(usableScreens.numScreens)",
            service: serviceName
        )

        // Get current window info for logging
        let windowInfo = try await accessibilityElement.windowInfo(for: window)
        logger.info(
            "Window Info - Frame: \(windowInfo.frame), Expected Screen: \(screenIdentifier(currentScreen))",
            service: serviceName
        )

        // Log screen arrangement for debugging vertical display issues
        logScreenArrangement(usableScreens.screens)

        // Log main display information for debugging coordinate flipping issues
        logMainDisplayInfo()

        // For basic snap operations (like topHalf, bottomHalf), the window should stay on the current screen
        // This is the key difference from the old approach which was incorrectly moving windows between screens
        let targetScreen = currentScreen

        logger.info(
            "Target Screen Decision - Expected: \(screenIdentifier(currentScreen)), "
                + "Actual Target: \(screenIdentifier(targetScreen)) (should be same for basic snaps)",
            service: serviceName
        )

        // Move window using Rectangle's approach
        try await windowMover.moveWindow(window, to: direction, on: targetScreen)

        // Verify the operation
        let newWindowInfo = try await accessibilityElement.windowInfo(for: window)
        let actualScreen = screenDetection.getScreenContaining(newWindowInfo.frame)

        logger.info(
            "Window Snap Result - Position: \(position), "
                + "Expected Screen: \(screenIdentifier(targetScreen)), "
                + "Actual Screen: \(actualScreen.map(screenIdentifier) ?? "Unknown"), "
                + "Window Frame Before: \(windowInfo.frame), "
                + "Window Frame After: \(newWindowInfo.frame)",
            service: serviceName
        )

        if let actualScreen = actualScreen, actualScreen != targetScreen {
            logger.warning(
                "ISSUE DETECTED: Window ended up on wrong screen! "
                    + "Expected: \(screenIdentifier(targetScreen)), "
                    + "Actual: \(screenIdentifier(actualScreen))",
                service: serviceName
            )
        }

        logger.info("Successfully snapped window to \(position)", service: serviceName)
    }

    private func getFrontmostWindow() async throws -> AXUIElement? {
        logger.debug("🔍 SANDBOX DIAGNOSTIC: Starting getFrontmostWindow()", service: serviceName)

        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.warning(
                "🔴 SANDBOX DIAGNOSTIC: No frontmost application found", service: serviceName)
            return nil
        }

        logger.debug(
            "🔍 SANDBOX DIAGNOSTIC: Frontmost app: \(frontmostApp.localizedName ?? "Unknown") (PID: \(frontmostApp.processIdentifier))",
            service: serviceName
        )

        // SANDBOXING FIX: Check and request automation permission for the target app
        if let bundleIdentifier = frontmostApp.bundleIdentifier {
            let hasAutomationPermission = PermissionManager.shared.checkAutomationPermission(
                for: bundleIdentifier)
            logger.info(
                "AUTOMATION: Permission check for \(frontmostApp.localizedName ?? "Unknown") (\(bundleIdentifier)): \(hasAutomationPermission)",
                service: serviceName
            )

            if !hasAutomationPermission {
                logger.warning(
                    "AUTOMATION: No permission for \(frontmostApp.localizedName ?? "Unknown") - requesting permission",
                    service: serviceName
                )

                // Request native automation permission
                PermissionManager.shared.requestAutomationPermission(
                    for: bundleIdentifier,
                    appName: frontmostApp.localizedName ?? "Unknown"
                )

                // NATIVE PERMISSION FIX: Don't abort immediately - the native dialog may have granted permission
                // Check permission status again after the request
                let updatedPermission = PermissionManager.shared.checkAutomationPermission(
                    for: bundleIdentifier)
                logger.info(
                    "AUTOMATION: Permission status after request for \(frontmostApp.localizedName ?? "Unknown"): \(updatedPermission)",
                    service: serviceName
                )

                if !updatedPermission {
                    logger.warning(
                        "🔴 SANDBOX DIAGNOSTIC: Automation permission still not granted for \(frontmostApp.localizedName ?? "Unknown") - user may need to manually enable in System Settings",
                        service: serviceName
                    )
                    throw AccessibilityError.failedToGetAttribute(.apiDisabled)
                }
            }
        } else {
            logger.warning(
                "🔴 AUTOMATION DIAGNOSTIC: No bundle identifier for frontmost app - cannot check automation permissions",
                service: serviceName
            )
        }

        let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)

        // SANDBOXING FIX: Try multiple approaches to get a window

        // Approach 1: Try to get focused window (may fail in sandboxed apps)
        var focusedWindowRef: AnyObject?
        let focusedError = AXUIElementCopyAttributeValue(
            appElement,
            kAXFocusedWindowAttribute as CFString,
            &focusedWindowRef
        )

        if focusedError == .success, let element = focusedWindowRef {
            let focusedWindow = element as! AXUIElement
            logger.debug(
                "✅ SANDBOX DIAGNOSTIC: Successfully got focused window", service: serviceName)
            return focusedWindow
        } else {
            logger.warning(
                "🔴 SANDBOX DIAGNOSTIC: Failed to get focused window: \(focusedError) (\(focusedError.rawValue)) - trying alternative approaches",
                service: serviceName
            )
        }

        // Approach 2: Get all windows and return the first one (more reliable in sandboxed apps)
        var windowsRef: AnyObject?
        let windowsError = AXUIElementCopyAttributeValue(
            appElement,
            kAXWindowsAttribute as CFString,
            &windowsRef
        )

        if windowsError == .success, let windowsArray = windowsRef {
            let windows = windowsArray as! [AXUIElement]
            if !windows.isEmpty {
                logger.debug(
                    "✅ SANDBOX DIAGNOSTIC: Got \(windows.count) windows, using first window as fallback",
                    service: serviceName
                )
                return windows.first
            }
        }

        logger.error(
            "🔴 SANDBOX DIAGNOSTIC: Failed to get windows list: \(windowsError) (\(windowsError.rawValue))",
            service: serviceName
        )

        // Approach 3: Use the cursor-based approach as final fallback
        logger.debug(
            "🔍 SANDBOX DIAGNOSTIC: Trying cursor-based window detection as final fallback",
            service: serviceName)
        if let cursorWindow = AccessibilityElement.getWindowElementUnderCursor() {
            logger.debug(
                "✅ SANDBOX DIAGNOSTIC: Found window under cursor as fallback", service: serviceName)
            return cursorWindow
        }

        logger.error(
            "🔴 SANDBOX DIAGNOSTIC: All approaches failed to find a window", service: serviceName)
        throw AccessibilityError.failedToGetAttribute(focusedError)
    }

    // MARK: - Internal Methods
    internal func convertSnapPositionToDirection(_ position: SnapPosition) -> WindowDirection {
        logger.debug("Converting position \(position) to direction", service: serviceName)
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        case .custom(let rect): return .custom(rect)
        }
    }

    // MARK: - Helper Methods
    private func screenIdentifier(_ screen: NSScreen) -> String {
        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            return "Screen#\(screenNumber)"
        }
        return "Unknown"
    }

    private func logScreenArrangement(_ screens: [NSScreen]) {
        logger.info("Screen arrangement for window management:", service: serviceName)
        for (index, screen) in screens.enumerated() {
            let identifier = screenIdentifier(screen)
            let isMain = screen == NSScreen.main ? " (MAIN)" : ""
            logger.info(
                "  \(index): \(identifier) - frame: \(screen.frame)\(isMain)",
                service: serviceName
            )
        }
    }

    private func logMainDisplayInfo() {
        // Log information about main display detection for debugging coordinate issues
        let nsScreenMain = NSScreen.main
        let nsScreenFirst = NSScreen.screens.first

        // Find the actual main display (coordinates 0,0)
        let actualMainDisplay = NSScreen.screens.first { screen in
            screen.frame.minX == 0 && screen.frame.minY == 0
        }

        logger.info("Main Display Analysis:", service: serviceName)
        logger.info(
            "  NSScreen.main: \(nsScreenMain.map(screenIdentifier) ?? "nil") - frame: \(nsScreenMain?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  NSScreen.screens[0]: \(nsScreenFirst.map(screenIdentifier) ?? "nil") - frame: \(nsScreenFirst?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  Actual main (0,0): \(actualMainDisplay.map(screenIdentifier) ?? "nil") - frame: \(actualMainDisplay?.frame ?? .zero)",
            service: serviceName)

        if let nsMain = nsScreenMain, let actualMain = actualMainDisplay, nsMain != actualMain {
            logger.warning(
                "COORDINATE ISSUE: NSScreen.main (\(screenIdentifier(nsMain))) differs from actual main display (\(screenIdentifier(actualMain)))",
                service: serviceName
            )
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}
