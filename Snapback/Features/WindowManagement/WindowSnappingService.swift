import AppKit
import Carbon

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared

    // MARK: - Dependencies
    private let windowMover: WindowMover
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let accessibilityElement: AccessibilityElement

    // MARK: - Initialization
    init(
        windowMover: WindowMover = WindowMover(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService(),
        accessibilityElement: AccessibilityElement = AccessibilityElement()
    ) {
        self.windowMover = windowMover
        self.calculationService = calculationService
        self.screenDetection = screenDetection
        self.accessibilityElement = accessibilityElement
        logger.info("Initialized with dependencies", service: serviceName)
    }

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        Task {
            do {
                // SANDBOXED SOLUTION: Try accessibility APIs first, fall back to AppleScript
                let window = try await getFrontmostWindow()
                try await snapWindow(window, to: position)
            } catch {
                // If accessibility APIs fail, try AppleScript-based approach
                logger.info(
                    "🔍 SANDBOX DIAGNOSTIC: Accessibility approach failed, trying AppleScript fallback for position: \(position)",
                    service: serviceName
                )

                do {
                    try await snapWindowWithAppleScriptFallback(to: position)
                } catch {
                    logger.error(
                        "Error snapping window with both approaches: \(error)", service: serviceName
                    )
                }
            }
        }
    }

    /// SANDBOXED FALLBACK: Snap window using pure AppleScript approach
    private func snapWindowWithAppleScriptFallback(to position: SnapPosition) async throws {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication,
            let bundleIdentifier = frontmostApp.bundleIdentifier
        else {
            throw WindowSnappingError.noFrontmostWindow
        }

        // Get the correct application name for AppleScript
        let appName = getAppleScriptApplicationName(for: frontmostApp)
        let direction = convertSnapPositionToDirection(position)

        // Use main screen as default for AppleScript approach
        let screen = NSScreen.main ?? NSScreen.screens.first!

        logger.info(
            "🚀 APPLESCRIPT FALLBACK: Starting pure AppleScript window management for \(appName) - Position: \(position), Direction: \(direction)",
            service: serviceName
        )

        // Calculate target frame
        let targetFrame = calculateAppleScriptTargetFrame(direction: direction, screen: screen)

        // Execute AppleScript
        let success = moveWindowWithAppleScript(
            bundleIdentifier: bundleIdentifier,
            appName: appName,
            targetFrame: targetFrame
        )

        if !success {
            throw WindowSnappingError.noFrontmostWindow
        }

        logger.info(
            "✅ APPLESCRIPT FALLBACK: Successfully snapped window to \(position)",
            service: serviceName)
    }

    // MARK: - Private Methods
    private func snapWindow(_ window: AXUIElement?, to position: SnapPosition) async throws {
        guard let window = window else {
            logger.warning("Window is nil", service: serviceName)
            throw WindowSnappingError.noFrontmostWindow
        }

        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()

        // Use Rectangle's screen detection approach
        guard let usableScreens = screenDetection.detectScreens(using: window) else {
            logger.warning(
                "Failed to detect screens using Rectangle's approach", service: serviceName)
            throw WindowSnappingError.noTargetScreen
        }

        let currentScreen = usableScreens.currentScreen
        let direction = convertSnapPositionToDirection(position)

        // Log comprehensive screen and window information
        logger.info(
            "Window Management Operation - Position: \(position), Direction: \(direction), "
                + "Current Screen: \(screenIdentifier(currentScreen)), Total Screens: \(usableScreens.numScreens)",
            service: serviceName
        )

        // Get current window info for logging
        let windowInfo = try await accessibilityElement.windowInfo(for: window)
        logger.info(
            "Window Info - Frame: \(windowInfo.frame), Expected Screen: \(screenIdentifier(currentScreen))",
            service: serviceName
        )

        // Log screen arrangement for debugging vertical display issues
        logScreenArrangement(usableScreens.screens)

        // Log main display information for debugging coordinate flipping issues
        logMainDisplayInfo()

        // For basic snap operations (like topHalf, bottomHalf), the window should stay on the current screen
        // This is the key difference from the old approach which was incorrectly moving windows between screens
        let targetScreen = currentScreen

        logger.info(
            "Target Screen Decision - Expected: \(screenIdentifier(currentScreen)), "
                + "Actual Target: \(screenIdentifier(targetScreen)) (should be same for basic snaps)",
            service: serviceName
        )

        // Move window using Rectangle's approach
        try await windowMover.moveWindow(window, to: direction, on: targetScreen)

        // Verify the operation
        let newWindowInfo = try await accessibilityElement.windowInfo(for: window)
        let actualScreen = screenDetection.getScreenContaining(newWindowInfo.frame)

        logger.info(
            "Window Snap Result - Position: \(position), "
                + "Expected Screen: \(screenIdentifier(targetScreen)), "
                + "Actual Screen: \(actualScreen.map(screenIdentifier) ?? "Unknown"), "
                + "Window Frame Before: \(windowInfo.frame), "
                + "Window Frame After: \(newWindowInfo.frame)",
            service: serviceName
        )

        if let actualScreen = actualScreen, actualScreen != targetScreen {
            logger.warning(
                "ISSUE DETECTED: Window ended up on wrong screen! "
                    + "Expected: \(screenIdentifier(targetScreen)), "
                    + "Actual: \(screenIdentifier(actualScreen))",
                service: serviceName
            )
        }

        logger.info("Successfully snapped window to \(position)", service: serviceName)
    }

    private func getFrontmostWindow(direction: WindowDirection? = nil, screen: NSScreen? = nil)
        async throws -> AXUIElement?
    {
        logger.debug("🔍 SANDBOX DIAGNOSTIC: Starting getFrontmostWindow()", service: serviceName)

        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.warning(
                "🔴 SANDBOX DIAGNOSTIC: No frontmost application found", service: serviceName)
            return nil
        }

        logger.debug(
            "🔍 SANDBOX DIAGNOSTIC: Frontmost app: \(frontmostApp.localizedName ?? "Unknown") (PID: \(frontmostApp.processIdentifier))",
            service: serviceName
        )

        // CRITICAL FIX: Check BOTH automation AND accessibility permissions
        if let bundleIdentifier = frontmostApp.bundleIdentifier {
            let appName = frontmostApp.localizedName ?? "Unknown"

            // Check automation permission (AppleScript/Apple Events)
            let hasAutomationPermission = PermissionManager.shared.checkAutomationPermission(
                for: bundleIdentifier)
            logger.info(
                "AUTOMATION: AppleScript permission for \(appName) (\(bundleIdentifier)): \(hasAutomationPermission)",
                service: serviceName
            )

            // Check accessibility permission (Window Management APIs)
            let hasAccessibilityPermission = PermissionManager.shared.checkAccessibilityPermission(
                for: bundleIdentifier)
            logger.info(
                "ACCESSIBILITY: Window management permission for \(appName) (\(bundleIdentifier)): \(hasAccessibilityPermission)",
                service: serviceName
            )

            // Request automation permission if needed
            if !hasAutomationPermission {
                logger.warning(
                    "AUTOMATION: No AppleScript permission for \(appName) - requesting permission",
                    service: serviceName
                )

                PermissionManager.shared.requestAutomationPermission(
                    for: bundleIdentifier,
                    appName: appName
                )
            }

            // Check if we have accessibility permission (this is what we actually need for window management)
            if !hasAccessibilityPermission {
                logger.warning(
                    "🔴 ACCESSIBILITY: No window management permission for \(appName) - this requires manual setup in System Settings",
                    service: serviceName
                )
                logger.warning(
                    "🔴 ACCESSIBILITY: Go to System Settings > Privacy & Security > Accessibility and enable Snapback",
                    service: serviceName
                )
                throw AccessibilityError.failedToGetAttribute(.apiDisabled)
            }

            logger.info(
                "✅ PERMISSIONS: Both automation (\(hasAutomationPermission)) and accessibility (\(hasAccessibilityPermission)) permissions verified for \(appName)",
                service: serviceName
            )
        } else {
            logger.warning(
                "🔴 AUTOMATION DIAGNOSTIC: No bundle identifier for frontmost app - cannot check automation permissions",
                service: serviceName
            )
        }

        let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)

        // SANDBOXING FIX: Try multiple approaches to get a window

        // Approach 1: Try to get focused window (may fail in sandboxed apps)
        var focusedWindowRef: AnyObject?
        let focusedError = AXUIElementCopyAttributeValue(
            appElement,
            kAXFocusedWindowAttribute as CFString,
            &focusedWindowRef
        )

        if focusedError == .success, let element = focusedWindowRef {
            let focusedWindow = element as! AXUIElement
            logger.debug(
                "✅ SANDBOX DIAGNOSTIC: Successfully got focused window", service: serviceName)
            return focusedWindow
        } else {
            logger.warning(
                "🔴 SANDBOX DIAGNOSTIC: Failed to get focused window: \(focusedError) (\(focusedError.rawValue)) - trying alternative approaches",
                service: serviceName
            )
        }

        // Approach 2: Get all windows and return the first one (more reliable in sandboxed apps)
        var windowsRef: AnyObject?
        let windowsError = AXUIElementCopyAttributeValue(
            appElement,
            kAXWindowsAttribute as CFString,
            &windowsRef
        )

        if windowsError == .success, let windowsArray = windowsRef {
            let windows = windowsArray as! [AXUIElement]
            if !windows.isEmpty {
                logger.debug(
                    "✅ SANDBOX DIAGNOSTIC: Got \(windows.count) windows, using first window as fallback",
                    service: serviceName
                )
                return windows.first
            }
        }

        logger.error(
            "🔴 SANDBOX DIAGNOSTIC: Failed to get windows list: \(windowsError) (\(windowsError.rawValue))",
            service: serviceName
        )

        // Approach 3: Use AppleScript-based window management (SANDBOXED SOLUTION)
        logger.info(
            "🔍 SANDBOX DIAGNOSTIC: Accessibility APIs blocked - switching to AppleScript-based window management",
            service: serviceName)

        if let bundleIdentifier = frontmostApp.bundleIdentifier,
            let direction = direction,
            let screen = screen
        {
            return try performAppleScriptWindowSnapping(
                for: bundleIdentifier,
                appName: frontmostApp.localizedName ?? "Unknown",
                direction: direction,
                screen: screen
            )
        }

        logger.error(
            "🔴 SANDBOX DIAGNOSTIC: All approaches failed - no bundle identifier available",
            service: serviceName)
        throw AccessibilityError.failedToGetAttribute(focusedError)
    }

    /// SANDBOXED SOLUTION: Perform window snapping using AppleScript instead of accessibility APIs
    private func performAppleScriptWindowSnapping(
        for bundleIdentifier: String,
        appName: String,
        direction: WindowDirection,
        screen: NSScreen
    ) throws -> AXUIElement {
        logger.info(
            "🚀 APPLESCRIPT: Starting AppleScript-based window management for \(appName) - Direction: \(direction)",
            service: serviceName
        )

        // Calculate target frame using existing calculation service
        let screenFrame = screen.visibleFrame
        let targetFrame = calculateAppleScriptTargetFrame(direction: direction, screen: screen)

        logger.info(
            "🚀 APPLESCRIPT: Target frame calculated - \(targetFrame) on screen \(screenFrame)",
            service: serviceName
        )

        // Use AppleScript to move the window
        let success = moveWindowWithAppleScript(
            bundleIdentifier: bundleIdentifier,
            appName: appName,
            targetFrame: targetFrame
        )

        if success {
            logger.info(
                "✅ APPLESCRIPT: Window snapping completed successfully for \(appName)",
                service: serviceName
            )

            // Return a dummy AXUIElement since we can't get the actual element in sandboxed mode
            // The window has been moved successfully via AppleScript
            let frontmostApp = NSWorkspace.shared.frontmostApplication!
            return AXUIElementCreateApplication(frontmostApp.processIdentifier)
        } else {
            logger.error(
                "❌ APPLESCRIPT: Window snapping failed for \(appName)",
                service: serviceName
            )
            throw AccessibilityError.failedToGetAttribute(.apiDisabled)
        }
    }

    /// Calculate target frame for AppleScript-based window management
    private func calculateAppleScriptTargetFrame(direction: WindowDirection, screen: NSScreen)
        -> CGRect
    {
        let visibleFrame = screen.visibleFrame

        switch direction {
        case .leftHalf:
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY,
                width: visibleFrame.width / 2,
                height: visibleFrame.height
            )
        case .rightHalf:
            return CGRect(
                x: visibleFrame.minX + visibleFrame.width / 2,
                y: visibleFrame.minY,
                width: visibleFrame.width / 2,
                height: visibleFrame.height
            )
        case .topHalf:
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY,
                width: visibleFrame.width,
                height: visibleFrame.height / 2
            )
        case .bottomHalf:
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY + visibleFrame.height / 2,
                width: visibleFrame.width,
                height: visibleFrame.height / 2
            )
        case .topLeft:
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY,
                width: visibleFrame.width / 2,
                height: visibleFrame.height / 2
            )
        case .topRight:
            return CGRect(
                x: visibleFrame.minX + visibleFrame.width / 2,
                y: visibleFrame.minY,
                width: visibleFrame.width / 2,
                height: visibleFrame.height / 2
            )
        case .bottomLeft:
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY + visibleFrame.height / 2,
                width: visibleFrame.width / 2,
                height: visibleFrame.height / 2
            )
        case .bottomRight:
            return CGRect(
                x: visibleFrame.minX + visibleFrame.width / 2,
                y: visibleFrame.minY + visibleFrame.height / 2,
                width: visibleFrame.width / 2,
                height: visibleFrame.height / 2
            )
        case .maximize:
            return visibleFrame
        case .center:
            let centerWidth = visibleFrame.width * 0.8
            let centerHeight = visibleFrame.height * 0.8
            return CGRect(
                x: visibleFrame.minX + (visibleFrame.width - centerWidth) / 2,
                y: visibleFrame.minY + (visibleFrame.height - centerHeight) / 2,
                width: centerWidth,
                height: centerHeight
            )
        default:
            // Handle all other cases (thirds, quarters, etc.) with basic left half as fallback
            return CGRect(
                x: visibleFrame.minX,
                y: visibleFrame.minY,
                width: visibleFrame.width / 2,
                height: visibleFrame.height
            )
        }
    }

    /// Move window using AppleScript (works in sandboxed apps)
    private func moveWindowWithAppleScript(
        bundleIdentifier: String,
        appName: String,
        targetFrame: CGRect
    ) -> Bool {
        logger.info(
            "🚀 APPLESCRIPT: Executing window move script for \(appName) to frame \(targetFrame)",
            service: serviceName
        )

        // Convert Cocoa coordinates to AppleScript coordinates (flip Y axis)
        let screenHeight = NSScreen.main?.frame.height ?? 1080
        let scriptFrame = CGRect(
            x: targetFrame.minX,
            y: screenHeight - targetFrame.maxY,
            width: targetFrame.width,
            height: targetFrame.height
        )

        // Try multiple AppleScript approaches for better compatibility
        let scripts = [
            // Method 1: Use application name (most reliable)
            """
            tell application "\(appName)"
                try
                    activate
                    tell front window
                        set bounds to {\(Int(scriptFrame.minX)), \(Int(scriptFrame.minY)), \(Int(scriptFrame.maxX)), \(Int(scriptFrame.maxY))}
                    end tell
                    return "success"
                on error errMsg number errNum
                    return "error: " & errNum & " - " & errMsg
                end try
            end tell
            """,

            // Method 2: Use System Events (most compatible with sandboxed apps)
            """
            tell application "System Events"
                tell application process "\(appName)"
                    try
                        set frontmost to true
                        tell front window
                            set position to {\(Int(scriptFrame.minX)), \(Int(scriptFrame.minY))}
                            set size to {\(Int(scriptFrame.width)), \(Int(scriptFrame.height))}
                        end tell
                        return "success"
                    on error errMsg number errNum
                        return "error: " & errNum & " - " & errMsg
                    end try
                end tell
            end tell
            """,

            // Method 3: Use bundle identifier (fallback)
            """
            tell application id "\(bundleIdentifier)"
                try
                    activate
                    tell front window
                        set bounds to {\(Int(scriptFrame.minX)), \(Int(scriptFrame.minY)), \(Int(scriptFrame.maxX)), \(Int(scriptFrame.maxY))}
                    end tell
                    return "success"
                on error errMsg number errNum
                    return "error: " & errNum & " - " & errMsg
                end try
            end tell
            """,
        ]

        // Try each script method until one succeeds
        for (index, script) in scripts.enumerated() {
            logger.info(
                "🚀 APPLESCRIPT: Trying method \(index + 1) for \(appName)",
                service: serviceName
            )

            var error: NSDictionary?
            let appleScript = NSAppleScript(source: script)
            let result = appleScript?.executeAndReturnError(&error)

            if let error = error {
                let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
                logger.info(
                    "🚀 APPLESCRIPT: Method \(index + 1) error \(errorCode) for \(appName) - \(error)",
                    service: serviceName
                )
                continue  // Try next method
            } else if let result = result {
                let resultString = result.stringValue ?? ""
                logger.info(
                    "🚀 APPLESCRIPT: Method \(index + 1) result for \(appName) - \(resultString)",
                    service: serviceName
                )
                if resultString.contains("success") {
                    logger.info(
                        "✅ APPLESCRIPT: Method \(index + 1) succeeded for \(appName)",
                        service: serviceName
                    )
                    return true
                }
            }
        }

        logger.warning(
            "❌ APPLESCRIPT: All methods failed for \(appName)",
            service: serviceName
        )
        return false
    }

    /// Get the correct application name for AppleScript
    private func getAppleScriptApplicationName(for app: NSRunningApplication) -> String {
        // Try multiple name sources in order of preference for AppleScript compatibility
        let bundleIdentifier = app.bundleIdentifier ?? ""

        // Special cases for common applications that have different display vs AppleScript names
        let nameMapping: [String: String] = [
            "com.microsoft.VSCode": "Visual Studio Code",
            "com.apple.dt.Xcode": "Xcode",
            "company.thebrowser.Browser": "Arc",
            "com.apple.finder": "Finder",
            "com.apple.Safari": "Safari",
            "com.google.Chrome": "Google Chrome",
            "com.mozilla.firefox": "Firefox",
            "com.apple.Terminal": "Terminal",
            "com.apple.TextEdit": "TextEdit",
            "com.apple.Preview": "Preview",
            "com.apple.Music": "Music",
            "com.apple.TV": "TV",
            "com.apple.Photos": "Photos",
            "com.apple.Notes": "Notes",
            "com.apple.Reminders": "Reminders",
            "com.apple.Calendar": "Calendar",
            "com.apple.Mail": "Mail",
            "com.apple.Messages": "Messages",
            "com.apple.FaceTime": "FaceTime",
        ]

        // Check if we have a known mapping
        if let mappedName = nameMapping[bundleIdentifier] {
            logger.info(
                "🚀 APPLESCRIPT: Using mapped name '\(mappedName)' for bundle '\(bundleIdentifier)'",
                service: serviceName
            )
            return mappedName
        }

        // Try to get the actual application name from the bundle
        if let bundlePath = app.bundleURL?.path,
            let bundle = Bundle(path: bundlePath)
        {

            // Try CFBundleDisplayName first (most accurate for AppleScript)
            if let displayName = bundle.object(forInfoDictionaryKey: "CFBundleDisplayName")
                as? String,
                !displayName.isEmpty
            {
                logger.info(
                    "🚀 APPLESCRIPT: Using CFBundleDisplayName '\(displayName)' for bundle '\(bundleIdentifier)'",
                    service: serviceName
                )
                return displayName
            }

            // Try CFBundleName as fallback
            if let bundleName = bundle.object(forInfoDictionaryKey: "CFBundleName") as? String,
                !bundleName.isEmpty
            {
                logger.info(
                    "🚀 APPLESCRIPT: Using CFBundleName '\(bundleName)' for bundle '\(bundleIdentifier)'",
                    service: serviceName
                )
                return bundleName
            }
        }

        // Fallback to localized name
        let fallbackName = app.localizedName ?? "Unknown"
        logger.info(
            "🚀 APPLESCRIPT: Using fallback localizedName '\(fallbackName)' for bundle '\(bundleIdentifier)'",
            service: serviceName
        )
        return fallbackName
    }

    // MARK: - Internal Methods
    internal func convertSnapPositionToDirection(_ position: SnapPosition) -> WindowDirection {
        logger.debug("Converting position \(position) to direction", service: serviceName)
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        case .custom(let rect): return .custom(rect)
        }
    }

    // MARK: - Helper Methods
    private func screenIdentifier(_ screen: NSScreen) -> String {
        if let screenNumber = screen.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")]
            as? NSNumber
        {
            return "Screen#\(screenNumber)"
        }
        return "Unknown"
    }

    private func logScreenArrangement(_ screens: [NSScreen]) {
        logger.info("Screen arrangement for window management:", service: serviceName)
        for (index, screen) in screens.enumerated() {
            let identifier = screenIdentifier(screen)
            let isMain = screen == NSScreen.main ? " (MAIN)" : ""
            logger.info(
                "  \(index): \(identifier) - frame: \(screen.frame)\(isMain)",
                service: serviceName
            )
        }
    }

    private func logMainDisplayInfo() {
        // Log information about main display detection for debugging coordinate issues
        let nsScreenMain = NSScreen.main
        let nsScreenFirst = NSScreen.screens.first

        // Find the actual main display (coordinates 0,0)
        let actualMainDisplay = NSScreen.screens.first { screen in
            screen.frame.minX == 0 && screen.frame.minY == 0
        }

        logger.info("Main Display Analysis:", service: serviceName)
        logger.info(
            "  NSScreen.main: \(nsScreenMain.map(screenIdentifier) ?? "nil") - frame: \(nsScreenMain?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  NSScreen.screens[0]: \(nsScreenFirst.map(screenIdentifier) ?? "nil") - frame: \(nsScreenFirst?.frame ?? .zero)",
            service: serviceName)
        logger.info(
            "  Actual main (0,0): \(actualMainDisplay.map(screenIdentifier) ?? "nil") - frame: \(actualMainDisplay?.frame ?? .zero)",
            service: serviceName)

        if let nsMain = nsScreenMain, let actualMain = actualMainDisplay, nsMain != actualMain {
            logger.warning(
                "COORDINATE ISSUE: NSScreen.main (\(screenIdentifier(nsMain))) differs from actual main display (\(screenIdentifier(actualMain)))",
                service: serviceName
            )
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}
