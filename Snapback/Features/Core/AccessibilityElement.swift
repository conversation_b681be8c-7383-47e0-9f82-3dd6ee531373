import AppKit
import Carbon

// Define custom accessibility attributes
extension NSAccessibility.Attribute {
    static let minimumSize = NSAccessibility.Attribute(rawValue: "AXMinimumSize")
    static let sizeConstraints = NSAccessibility.Attribute(rawValue: "AXSizeConstraints")
}

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
public class AccessibilityElement {
    // MARK: - Static Methods

    /// Gets the window element under the cursor
    static func getWindowElementUnderCursor() -> AXUIElement? {
        // Get the current mouse location
        let mouseLocation = NSEvent.mouseLocation

        // Log the mouse location for debugging
        LoggingService.shared.debug(
            "Looking for window at mouse location: \(mouseLocation)",
            service: "AccessibilityElement"
        )

        // Try multiple approaches to find the window

        // Approach 1: Use CGWindowList to find windows at the mouse location
        if let window = getWindowUsingCGWindowList(at: mouseLocation) {
            LoggingService.shared.debug(
                "Found window using CGWindowList approach",
                service: "AccessibilityElement"
            )
            return window
        }

        // Approach 2: Get the frontmost application and try to find its main window
        if let window = getFrontmostApplicationWindow() {
            LoggingService.shared.debug(
                "Found window using frontmost application approach",
                service: "AccessibilityElement"
            )
            return window
        }

        // Approach 3: Try all visible applications
        if let window = getAnyVisibleApplicationWindow() {
            LoggingService.shared.debug(
                "Found window using visible applications approach",
                service: "AccessibilityElement"
            )
            return window
        }

        LoggingService.shared.warning(
            "Failed to find any window under cursor at \(mouseLocation)",
            service: "AccessibilityElement"
        )

        return nil
    }

    /// Gets a window using CGWindowList at the specified location
    private static func getWindowUsingCGWindowList(at mouseLocation: NSPoint) -> AXUIElement? {
        // Get the window info at the mouse location
        let options: CGWindowListOption = [.optionOnScreenOnly]
        let windowListInfo =
            CGWindowListCopyWindowInfo(options, kCGNullWindowID) as? [[String: Any]]

        guard let windowList = windowListInfo else { return nil }

        LoggingService.shared.debug(
            "Found \(windowList.count) windows in CGWindowList",
            service: "AccessibilityElement"
        )

        // Find the frontmost window at the mouse location
        for windowInfo in windowList {
            // Skip windows not in layer 0 (normal application windows)
            guard let layer = windowInfo[kCGWindowLayer as String] as? Int, layer == 0,
                let ownerPID = windowInfo[kCGWindowOwnerPID as String] as? Int,
                let windowID = windowInfo[kCGWindowNumber as String] as? Int,
                let boundsDict = windowInfo[kCGWindowBounds as String] as? [String: Any],
                let windowName = windowInfo[kCGWindowName as String] as? String
            else {
                continue
            }

            // Log window info for debugging
            LoggingService.shared.debug(
                "Checking window: \(windowName), PID: \(ownerPID), ID: \(windowID)",
                service: "AccessibilityElement"
            )

            // Convert bounds dictionary to CGRect
            var bounds = CGRect.zero
            if let cfDict = boundsDict as CFDictionary?,
                CGRectMakeWithDictionaryRepresentation(cfDict, &bounds)
            {
                // Log bounds for debugging
                LoggingService.shared.debug(
                    "Window bounds: \(bounds)",
                    service: "AccessibilityElement"
                )

                // Check if the mouse is inside this window
                if bounds.contains(CGPoint(x: mouseLocation.x, y: mouseLocation.y)) {
                    LoggingService.shared.debug(
                        "Mouse is inside window: \(windowName)",
                        service: "AccessibilityElement"
                    )

                    // Create an accessibility element for this window
                    let appElement = AXUIElementCreateApplication(pid_t(ownerPID))
                    var windowsRef: CFTypeRef?
                    let error = AXUIElementCopyAttributeValue(
                        appElement, kAXWindowsAttribute as CFString, &windowsRef)

                    if error == .success, let windows = windowsRef as? [AXUIElement] {
                        LoggingService.shared.debug(
                            "Found \(windows.count) accessibility windows for app",
                            service: "AccessibilityElement"
                        )

                        // Find the window with matching ID
                        for window in windows {
                            if let windowId = getWindowID(window), windowId == windowID {
                                LoggingService.shared.debug(
                                    "Found matching window by ID",
                                    service: "AccessibilityElement"
                                )
                                return window
                            }
                        }
                    }

                    // If we couldn't find the exact window, return the first window of the app
                    if error == .success, let windows = windowsRef as? [AXUIElement],
                        let firstWindow = windows.first
                    {
                        LoggingService.shared.debug(
                            "Returning first window of app as fallback",
                            service: "AccessibilityElement"
                        )
                        return firstWindow
                    }
                }
            }
        }

        return nil
    }

    /// Gets the main window of the frontmost application
    private static func getFrontmostApplicationWindow() -> AXUIElement? {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            return nil
        }

        LoggingService.shared.debug(
            "Trying frontmost app: \(frontmostApp.localizedName ?? "Unknown")",
            service: "AccessibilityElement"
        )

        let pid = frontmostApp.processIdentifier
        let appElement = AXUIElementCreateApplication(pid)

        var windowsRef: CFTypeRef?
        let error = AXUIElementCopyAttributeValue(
            appElement, kAXWindowsAttribute as CFString, &windowsRef)

        if error == .success, let windows = windowsRef as? [AXUIElement], !windows.isEmpty {
            // Try to find the focused window first
            var focusedWindowRef: CFTypeRef?
            let focusedError = AXUIElementCopyAttributeValue(
                appElement, kAXFocusedWindowAttribute as CFString, &focusedWindowRef)

            if focusedError == .success, let focusedWindowRef = focusedWindowRef {
                let focusedWindow = focusedWindowRef as! AXUIElement
                LoggingService.shared.debug(
                    "✅ SANDBOX DIAGNOSTIC: Found focused window of frontmost app",
                    service: "AccessibilityElement"
                )
                return focusedWindow
            } else {
                LoggingService.shared.warning(
                    "🔴 SANDBOX DIAGNOSTIC: Failed to get focused window: \(focusedError) (\(focusedError.rawValue)) - using first window as fallback",
                    service: "AccessibilityElement"
                )
            }

            // If no focused window, return the first window (more reliable in sandboxed apps)
            LoggingService.shared.debug(
                "✅ SANDBOX DIAGNOSTIC: Returning first window of frontmost app as fallback",
                service: "AccessibilityElement"
            )
            return windows.first
        } else {
            LoggingService.shared.error(
                "🔴 SANDBOX DIAGNOSTIC: Failed to get windows for frontmost app: \(error) (\(error.rawValue))",
                service: "AccessibilityElement"
            )
        }

        return nil
    }

    /// Gets any visible application window
    private static func getAnyVisibleApplicationWindow() -> AXUIElement? {
        let runningApps = NSWorkspace.shared.runningApplications.filter {
            $0.activationPolicy == .regular && $0.isHidden == false
        }

        LoggingService.shared.debug(
            "Checking \(runningApps.count) visible applications",
            service: "AccessibilityElement"
        )

        for app in runningApps {
            let pid = app.processIdentifier
            let appElement = AXUIElementCreateApplication(pid)

            var windowsRef: CFTypeRef?
            let error = AXUIElementCopyAttributeValue(
                appElement, kAXWindowsAttribute as CFString, &windowsRef)

            if error == .success, let windows = windowsRef as? [AXUIElement], !windows.isEmpty {
                LoggingService.shared.debug(
                    "Found windows for app: \(app.localizedName ?? "Unknown")",
                    service: "AccessibilityElement"
                )
                return windows.first
            }
        }

        return nil
    }

    /// Gets the window ID for an accessibility element
    static func getWindowID(_ window: AXUIElement) -> CGWindowID? {
        var pid: pid_t = 0
        let error = AXUIElementGetPid(window, &pid)
        guard error == .success else { return nil }

        var windowIDRef: CFTypeRef?
        let errorCFNumber = AXUIElementCopyAttributeValue(
            window, "kCGWindowNumber" as CFString, &windowIDRef)

        if errorCFNumber == .success, let windowIDValue = windowIDRef as? CGWindowID {
            return windowIDValue
        }

        // If direct access fails, try to match by position and size
        // We'll use a synchronous approach instead of async/await
        do {
            // Get position and size directly without using async methods
            var positionRef: CFTypeRef?
            var sizeRef: CFTypeRef?

            let positionError = AXUIElementCopyAttributeValue(
                window, kAXPositionAttribute as CFString, &positionRef)
            let sizeError = AXUIElementCopyAttributeValue(
                window, kAXSizeAttribute as CFString, &sizeRef)

            guard positionError == .success, sizeError == .success else {
                return nil
            }

            guard let positionValue = positionRef, let sizeValue = sizeRef else {
                return nil
            }

            var position = CGPoint.zero
            var size = CGSize.zero
            AXValueGetValue(positionValue as! AXValue, .cgPoint, &position)
            AXValueGetValue(sizeValue as! AXValue, .cgSize, &size)

            // Convert to Cocoa coordinates if needed
            if let screen = NSScreen.screens.first(where: {
                $0.frame.contains(CGPoint(x: position.x, y: position.y))
            }) {
                position.y = screen.frame.height - (position.y + size.height)
            }

            let windowFrame = CGRect(origin: position, size: size)

            let options: CGWindowListOption = [.optionOnScreenOnly]
            let windowListInfo =
                CGWindowListCopyWindowInfo(options, kCGNullWindowID) as? [[String: Any]]

            guard let windowList = windowListInfo else { return nil }

            for windowInfo in windowList {
                guard let ownerPID = windowInfo[kCGWindowOwnerPID as String] as? Int,
                    pid_t(ownerPID) == pid,
                    let wid = windowInfo[kCGWindowNumber as String] as? CGWindowID,
                    let boundsDict = windowInfo[kCGWindowBounds as String] as? [String: Any]
                else {
                    continue
                }

                var bounds = CGRect.zero
                if let cfDict = boundsDict as CFDictionary?,
                    CGRectMakeWithDictionaryRepresentation(cfDict, &bounds)
                {
                    // Compare the bounds (with some tolerance)
                    if abs(bounds.origin.x - windowFrame.origin.x) < 5
                        && abs(bounds.origin.y - windowFrame.origin.y) < 5
                        && abs(bounds.size.width - windowFrame.size.width) < 5
                        && abs(bounds.size.height - windowFrame.size.height) < 5
                    {
                        return wid
                    }
                }
            }
        }

        return nil
    }

    // MARK: - Public Interface

    /// Gets window information in Cocoa coordinates
    public func windowInfo(for window: AXUIElement) async throws -> WindowInfo {
        let frame = try await getFrame(window)
        let isFullScreen = try await isWindowFullScreen(window)

        // Determine which screen contains the window
        let windowCenter = CGPoint(x: frame.midX, y: frame.midY)
        let containingScreen =
            NSScreen.screens.first { screen in
                screen.frame.contains(windowCenter)
            } ?? NSScreen.main

        // Get the monitor ID for the containing screen
        let monitorID =
            containingScreen?.deviceDescription[NSDeviceDescriptionKey("NSScreenNumber")] as? UUID

        return WindowInfo(
            frame: frame,
            monitorID: monitorID,
            appBundleIdentifier: nil,
            isFullscreen: isFullScreen
        )
    }

    /// Gets window frame in Cocoa coordinates
    public func getFrame(_ window: AXUIElement) async throws -> CGRect {
        var position = try await getPosition(window)
        let size = try await getSize(window)  // Changed to let since it's not mutated

        // Convert from AX to Cocoa coordinates if needed
        if let screen = NSScreen.screens.first(where: {
            $0.frame.contains(CGPoint(x: position.x, y: position.y))
        }) {
            position.y = screen.frame.height - (position.y + size.height)
        }

        return CGRect(origin: position, size: size)
    }

    /// Sets window frame using Rectangle's approach
    /// - Parameters:
    ///   - window: The window to set the frame for
    ///   - frame: The frame to set (already flipped if needed)
    ///   - isBottomAligned: Whether the window is bottom-aligned (for bottom half, bottom left, bottom right, etc.)
    public func setFrame(
        _ window: AXUIElement, _ frame: CGRect, isBottomAligned: Bool = false
    )
        async throws
    {
        // Get the current frame for logging
        let currentFrame = try await getFrame(window)

        // Log the operation
        LoggingService.shared.debug(
            "Setting window frame from \(currentFrame) to \(frame) (using Rectangle approach)",
            service: "AccessibilityElement"
        )

        // Following Rectangle's approach: always set size first, then position, then size again
        // This matches how Rectangle handles window positioning
        try await setSize(window, frame.size)
        try await setPosition(window, frame.origin)
        try await setSize(window, frame.size)  // Set size again to ensure it's applied correctly

        // Verify the operation
        let newFrame = try await getFrame(window)
        LoggingService.shared.debug(
            "After setting frame, window is at: \(newFrame)",
            service: "AccessibilityElement"
        )
    }

    /// Helper to determine if screens are arranged vertically
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if any screen is positioned below another screen
        var hasVerticalStacking = false

        // For macOS, we need to be more careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Calculate the vertical overlap percentage
            let verticalOverlap =
                min(screen1.frame.maxY, screen2.frame.maxY)
                - max(screen1.frame.minY, screen2.frame.minY)
            let minHeight = min(screen1.frame.height, screen2.frame.height)
            let verticalOverlapPercentage = verticalOverlap / minHeight

            // Calculate the horizontal overlap percentage
            let horizontalOverlap =
                min(screen1.frame.maxX, screen2.frame.maxX)
                - max(screen1.frame.minX, screen2.frame.minX)
            let minWidth = min(screen1.frame.width, screen2.frame.width)
            let horizontalOverlapPercentage = horizontalOverlap / minWidth

            // If there's significant horizontal overlap and minimal vertical overlap,
            // it's likely a vertical arrangement
            hasVerticalStacking =
                horizontalOverlapPercentage > 0.5 && verticalOverlapPercentage < 0.2

            // Also check if one screen is completely above the other
            if !hasVerticalStacking {
                hasVerticalStacking =
                    (screen1.frame.minY >= screen2.frame.maxY)
                    || (screen2.frame.minY >= screen1.frame.maxY)
            }
        } else {
            // For more than 2 screens, use the original approach
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        return (totalHeight > totalWidth * 1.2) || hasVerticalStacking
    }

    // MARK: - Private AX Attribute Methods

    /// Get the minimum size of a window
    public func getMinimumSize(_ window: AXUIElement) async throws -> CGSize {
        // Try to get the minimum size attribute
        do {
            if let minSizeValue = try await getAttribute(window, .minimumSize) as? [CGFloat],
                minSizeValue.count >= 2
            {
                return CGSize(width: minSizeValue[0], height: minSizeValue[1])
            }
        } catch {
            LoggingService.shared.debug(
                "Failed to get minimum size attribute: \(error)",
                service: "AccessibilityElement"
            )
        }

        // Try to get the current size as a fallback
        do {
            let currentSize = try await getSize(window)
            // Use a reasonable minimum size based on the current size
            let minWidth = min(currentSize.width, 200)
            let minHeight = min(currentSize.height, 200)
            return CGSize(width: minWidth, height: minHeight)
        } catch {
            LoggingService.shared.debug(
                "Failed to get current size: \(error)",
                service: "AccessibilityElement"
            )
        }

        // Default to a reasonable minimum size if we couldn't get it from the window
        return CGSize(width: 200, height: 200)
    }

    public func getPosition(_ window: AXUIElement) async throws -> CGPoint {
        try await getAXValue(window, attribute: kAXPositionAttribute, type: .cgPoint)
    }

    public func getSize(_ window: AXUIElement) async throws -> CGSize {
        try await getAXValue(window, attribute: kAXSizeAttribute, type: .cgSize)
    }

    public func setPosition(_ window: AXUIElement, _ position: CGPoint) async throws {
        try await setAXValue(
            window, attribute: kAXPositionAttribute, value: position, type: .cgPoint)
    }

    public func setSize(_ window: AXUIElement, _ size: CGSize) async throws {
        try await setAXValue(window, attribute: kAXSizeAttribute, value: size, type: .cgSize)
    }

    public func isWindowFullScreen(_ window: AXUIElement) async throws -> Bool {
        var value: AnyObject?
        let error = AXUIElementCopyAttributeValue(window, "AXFullScreen" as CFString, &value)

        guard error == .success else {
            throw AccessibilityError.failedToGetAttribute(error)
        }

        return (value as? Bool) ?? false
    }

    public func setWindowFullScreen(_ window: AXUIElement, _ fullScreen: Bool) async throws {
        let error = AXUIElementSetAttributeValue(
            window,
            "AXFullScreen" as CFString,
            fullScreen as CFTypeRef
        )

        if error != .success {
            throw AccessibilityError.failedToSetAttribute(error)
        }
    }

    // MARK: - Helper Methods

    /// Get an accessibility attribute value
    public func getAttribute(_ element: AXUIElement, _ attribute: NSAccessibility.Attribute)
        async throws
        -> Any?
    {
        var value: CFTypeRef?
        let error = AXUIElementCopyAttributeValue(element, attribute.rawValue as CFString, &value)

        if error == .success {
            return value
        } else if error == .attributeUnsupported || error == .noValue {
            return nil
        } else {
            throw AccessibilityError.failedToGetAttribute(error)
        }
    }

    public func getAXValue<T>(_ element: AXUIElement, attribute: String, type: AXValueType)
        async throws -> T
    {
        var value: AnyObject?
        let error = AXUIElementCopyAttributeValue(element, attribute as CFString, &value)

        guard error == .success else {
            throw AccessibilityError.failedToGetAttribute(error)
        }

        let axValue = value as! AXValue

        switch type {
        case .cgPoint:
            var point = CGPoint.zero
            guard AXValueGetValue(axValue, type, &point),
                let result = point as? T
            else {
                throw AccessibilityError.invalidValue
            }
            return result

        case .cgSize:
            var size = CGSize.zero
            guard AXValueGetValue(axValue, type, &size),
                let result = size as? T
            else {
                throw AccessibilityError.invalidValue
            }
            return result

        case .cgRect:
            var rect = CGRect.zero
            guard AXValueGetValue(axValue, type, &rect),
                let result = rect as? T
            else {
                throw AccessibilityError.invalidValue
            }
            return result

        case .cfRange:
            var range = CFRange(location: 0, length: 0)
            guard AXValueGetValue(axValue, type, &range),
                let result = range as? T
            else {
                throw AccessibilityError.invalidValue
            }
            return result

        default:
            throw AccessibilityError.invalidValue
        }
    }

    public func setAXValue<T>(
        _ element: AXUIElement, attribute: String, value: T, type: AXValueType
    ) async throws {
        switch type {
        case .cgPoint:
            guard let point = value as? CGPoint else {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: Failed to cast value to CGPoint for attribute \(attribute)",
                    service: "AccessibilityElement"
                )
                throw AccessibilityError.failedToCreateValue
            }
            var pointCopy = point  // Create mutable copy for inout parameter
            guard let axValue = AXValueCreate(type, &pointCopy) else {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: Failed to create AXValue for CGPoint \(point) for attribute \(attribute)",
                    service: "AccessibilityElement"
                )
                throw AccessibilityError.failedToCreateValue
            }

            LoggingService.shared.debug(
                "🔍 SANDBOX DIAGNOSTIC: About to call AXUIElementSetAttributeValue for \(attribute) with value \(point)",
                service: "AccessibilityElement"
            )

            let error = AXUIElementSetAttributeValue(element, attribute as CFString, axValue)
            if error != AXError.success {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: AXUIElementSetAttributeValue failed for \(attribute) with error: \(error) (\(error.rawValue))",
                    service: "AccessibilityElement"
                )

                // Check if this is a sandboxing-related error
                if error == .apiDisabled {
                    LoggingService.shared.error(
                        "🔴 SANDBOX DIAGNOSTIC: API_DISABLED error - This suggests missing entitlements or sandboxing restrictions",
                        service: "AccessibilityElement"
                    )
                } else if error == .notImplemented {
                    LoggingService.shared.error(
                        "🔴 SANDBOX DIAGNOSTIC: NOT_IMPLEMENTED error - The target application may not support this accessibility feature",
                        service: "AccessibilityElement"
                    )
                }

                throw AccessibilityError.failedToSetAttribute(error)
            } else {
                LoggingService.shared.debug(
                    "✅ SANDBOX DIAGNOSTIC: Successfully set \(attribute) to \(point)",
                    service: "AccessibilityElement"
                )
            }

        case .cgSize:
            guard let size = value as? CGSize else {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: Failed to cast value to CGSize for attribute \(attribute)",
                    service: "AccessibilityElement"
                )
                throw AccessibilityError.failedToCreateValue
            }
            var sizeCopy = size  // Create mutable copy for inout parameter
            guard let axValue = AXValueCreate(type, &sizeCopy) else {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: Failed to create AXValue for CGSize \(size) for attribute \(attribute)",
                    service: "AccessibilityElement"
                )
                throw AccessibilityError.failedToCreateValue
            }

            LoggingService.shared.debug(
                "🔍 SANDBOX DIAGNOSTIC: About to call AXUIElementSetAttributeValue for \(attribute) with value \(size)",
                service: "AccessibilityElement"
            )

            let error = AXUIElementSetAttributeValue(element, attribute as CFString, axValue)
            if error != AXError.success {
                LoggingService.shared.error(
                    "🔴 SANDBOX DIAGNOSTIC: AXUIElementSetAttributeValue failed for \(attribute) with error: \(error) (\(error.rawValue))",
                    service: "AccessibilityElement"
                )

                // Check if this is a sandboxing-related error
                if error == .apiDisabled {
                    LoggingService.shared.error(
                        "🔴 SANDBOX DIAGNOSTIC: API_DISABLED error - This suggests missing entitlements or sandboxing restrictions",
                        service: "AccessibilityElement"
                    )
                } else if error == .notImplemented {
                    LoggingService.shared.error(
                        "🔴 SANDBOX DIAGNOSTIC: NOT_IMPLEMENTED error - The target application may not support this accessibility feature",
                        service: "AccessibilityElement"
                    )
                }

                throw AccessibilityError.failedToSetAttribute(error)
            } else {
                LoggingService.shared.debug(
                    "✅ SANDBOX DIAGNOSTIC: Successfully set \(attribute) to \(size)",
                    service: "AccessibilityElement"
                )
            }

        case .cgRect:
            guard let rect = value as? CGRect else {
                throw AccessibilityError.failedToCreateValue
            }
            var rectCopy = rect  // Create mutable copy for inout parameter
            guard let axValue = AXValueCreate(type, &rectCopy) else {
                throw AccessibilityError.failedToCreateValue
            }
            let error = AXUIElementSetAttributeValue(element, attribute as CFString, axValue)
            if error != AXError.success {
                throw AccessibilityError.failedToSetAttribute(error)
            }

        case .cfRange:
            guard let range = value as? CFRange else {
                throw AccessibilityError.failedToCreateValue
            }
            var rangeCopy = range  // Create mutable copy for inout parameter
            guard let axValue = AXValueCreate(type, &rangeCopy) else {
                throw AccessibilityError.failedToCreateValue
            }
            let error = AXUIElementSetAttributeValue(element, attribute as CFString, axValue)
            if error != AXError.success {
                throw AccessibilityError.failedToSetAttribute(error)
            }

        default:
            throw AccessibilityError.invalidValue
        }
    }
}
