import AppKit
import ApplicationServices  // For AXIsProcessTrusted
import Foundation
import OSAKit  // For Apple Events and automation permissions

/// Manages accessibility permissions for the app
class PermissionManager: ObservableObject {
    static let shared = PermissionManager()

    /// Published property to track if accessibility permissions are granted
    @Published var isAccessibilityPermissionGranted: Bool = false

    /// Timer to periodically check permissions
    private var permissionCheckTimer: Timer?

    /// Notification name for permission changes
    static let permissionStatusChanged = Notification.Name("PermissionStatusChanged")

    /// Logger for diagnostic purposes
    private let logger = LoggingService.shared
    private let serviceName = "PermissionManager"

    /// Counter for tracking permission checks
    private var permissionCheckCount: Int = 0

    /// Adaptive timing configuration
    private let checkIntervalWhenDenied: TimeInterval = 3.0  // Check every 3 seconds when denied
    private let checkIntervalWhenGranted: TimeInterval = 30.0  // Check every 30 seconds when granted
    private let maxChecksWhenGranted: Int = 5  // Stop frequent checking after 5 successful checks
    private var successfulGrantedChecks: Int = 0

    /// UserDefaults keys for persistence
    private enum PersistenceKeys {
        static let hasEverBeenGranted = "SnapbackAccessibilityPermissionEverGranted"
        static let lastKnownStatus = "SnapbackLastKnownAccessibilityStatus"
        static let lastCheckTimestamp = "SnapbackLastPermissionCheckTimestamp"
    }

    private init() {
        logger.info("🔍 PERMISSION DIAGNOSTIC: PermissionManager initializing", service: serviceName)

        // Check persistent state first
        let hasEverBeenGranted = UserDefaults.standard.bool(
            forKey: PersistenceKeys.hasEverBeenGranted)
        let lastKnownStatus = UserDefaults.standard.bool(forKey: PersistenceKeys.lastKnownStatus)
        let lastCheckTimestamp = UserDefaults.standard.double(
            forKey: PersistenceKeys.lastCheckTimestamp)

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent state - Ever granted: \(hasEverBeenGranted), Last known: \(lastKnownStatus), Last check: \(Date(timeIntervalSince1970: lastCheckTimestamp))",
            service: serviceName)

        // FIXED: Always perform fresh check on app launch to catch permission revocations
        // This ensures we don't rely solely on cached state which can be stale
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Performing fresh permission check on app launch",
            service: serviceName)

        isAccessibilityPermissionGranted = checkAccessibilityPermission()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Fresh check result: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // Set up adaptive timer based on actual permission status
        startAdaptiveTimer()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager initialization complete",
            service: serviceName)
    }

    /// Update persistent state when permissions change
    private func updatePersistentState(_ isGranted: Bool) {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Updating persistent state to: \(isGranted)",
            service: serviceName)

        // Update last known status
        UserDefaults.standard.set(isGranted, forKey: PersistenceKeys.lastKnownStatus)

        // Update timestamp
        UserDefaults.standard.set(
            Date().timeIntervalSince1970, forKey: PersistenceKeys.lastCheckTimestamp)

        // If permissions are granted for the first time, mark it
        if isGranted && !UserDefaults.standard.bool(forKey: PersistenceKeys.hasEverBeenGranted) {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permissions granted for the first time - marking as ever granted",
                service: serviceName)
            UserDefaults.standard.set(true, forKey: PersistenceKeys.hasEverBeenGranted)
        }

        // Force synchronization to ensure persistence
        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent state updated and synchronized",
            service: serviceName)
    }

    /// Start adaptive timer based on current permission status
    private func startAdaptiveTimer() {
        permissionCheckTimer?.invalidate()

        let interval: TimeInterval
        let reason: String

        if isAccessibilityPermissionGranted {
            if successfulGrantedChecks >= maxChecksWhenGranted {
                // Stop frequent checking after permissions have been stable
                interval = 120.0  // Check every 2 minutes
                reason = "permissions stable (2min interval)"
            } else {
                interval = checkIntervalWhenGranted
                reason = "permissions granted (30s interval)"
            }
        } else {
            interval = checkIntervalWhenDenied
            reason = "permissions denied (3s interval)"
            successfulGrantedChecks = 0  // Reset counter when denied
        }

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Starting adaptive timer - \(reason)",
            service: serviceName)

        permissionCheckTimer = Timer.scheduledTimer(
            timeInterval: interval,
            target: self,
            selector: #selector(checkPermissionStatus),
            userInfo: nil,
            repeats: true
        )
    }

    /// Check if accessibility permissions are granted
    func checkAccessibilityPermission() -> Bool {
        permissionCheckCount += 1
        let timestamp = Date()

        // Reduce logging frequency - only log every 10th check when permissions are granted
        let shouldLogDetails = !isAccessibilityPermissionGranted || (permissionCheckCount % 10 == 0)

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: About to call AXIsProcessTrusted() - Check #\(permissionCheckCount) at \(timestamp)",
                service: serviceName)
        }

        // CRITICAL FIX: Always use AXIsProcessTrusted() for routine checks
        // Only use AXIsProcessTrustedWithOptions() with prompt when explicitly requesting permissions
        let result = AXIsProcessTrusted()

        if shouldLogDetails || result != isAccessibilityPermissionGranted {
            // Always log when status might change, or periodically when stable
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: AXIsProcessTrusted() returned: \(result) - Check #\(permissionCheckCount) at \(timestamp)",
                service: serviceName)
        }

        // FIXED: Enhanced logging for app launch scenarios
        if permissionCheckCount == 1 {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: FIRST CHECK ON APP LAUNCH - Permission status = \(result), Sandboxed = \(isSandboxed())",
                service: serviceName)
        }

        // Update persistent state whenever we get a fresh result
        updatePersistentState(result)

        return result
    }

    /// Check if the app is running in a sandboxed environment
    private func isSandboxed() -> Bool {
        let environment = ProcessInfo.processInfo.environment
        return environment["APP_SANDBOX_CONTAINER_ID"] != nil
    }

    /// Request automation permission for a specific application using native dialog trigger
    func requestAutomationPermission(for bundleIdentifier: String, appName: String) {
        logger.info(
            "AUTOMATION: Requesting native permission for \(appName) (\(bundleIdentifier))",
            service: serviceName
        )

        guard
            NSWorkspace.shared.runningApplications.contains(where: {
                $0.bundleIdentifier == bundleIdentifier
            })
        else {
            logger.warning(
                "AUTOMATION: Target application \(appName) is not running",
                service: serviceName
            )
            showAutomationPermissionInstructions(for: appName, reason: "Application not running")
            return
        }

        // BREAKTHROUGH SOLUTION: Use Process to execute osascript directly
        // This is how Raycast and other successful sandboxed apps trigger the native dialog
        logger.info(
            "AUTOMATION: Using Process + osascript to trigger native automation permission dialog",
            service: serviceName
        )

        triggerNativeDialogWithProcess(for: bundleIdentifier, appName: appName)
    }

    /// BREAKTHROUGH: Use Process to execute osascript directly - this triggers native dialogs
    private func triggerNativeDialogWithProcess(for bundleIdentifier: String, appName: String) {
        logger.info(
            "AUTOMATION: Executing osascript via Process to trigger native permission dialog for \(appName)",
            service: serviceName
        )

        // Create the AppleScript that will definitely require automation permission
        let script = """
        tell application id "\(bundleIdentifier)"
            try
                get name
            on error errMsg number errNum
                return "Error: " & errNum
            end try
        end tell
        """

        // Use Process to execute osascript directly
        let process = Process()
        process.executableURL = URL(fileURLWithPath: "/usr/bin/osascript")
        process.arguments = ["-e", script]

        let pipe = Pipe()
        process.standardOutput = pipe
        process.standardError = pipe

        do {
            try process.run()

            logger.info(
                "AUTOMATION: osascript process started for \(appName) - native dialog should appear",
                service: serviceName
            )

            // Wait for completion in background
            DispatchQueue.global(qos: .background).async {
                process.waitUntilExit()

                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                let output = String(data: data, encoding: .utf8) ?? ""

                DispatchQueue.main.async {
                    self.logger.info(
                        "AUTOMATION: osascript completed for \(appName) - Exit code: \(process.terminationStatus), Output: \(output)",
                        service: self.serviceName
                    )

                    if process.terminationStatus != 0 {
                        self.logger.info(
                            "AUTOMATION: osascript error indicates permission dialog should have appeared",
                            service: self.serviceName
                        )
                    }
                }
            }

        } catch {
            logger.warning(
                "AUTOMATION: Failed to execute osascript process: \(error.localizedDescription)",
                service: serviceName
            )

            // Fallback to NSAppleScript approach
            fallbackToNSAppleScript(for: bundleIdentifier, appName: appName)
        }
    }

    /// Fallback to NSAppleScript if Process approach fails
    private func fallbackToNSAppleScript(for bundleIdentifier: String, appName: String) {
        logger.info(
            "AUTOMATION: Using NSAppleScript fallback for \(appName)",
            service: serviceName
        )

        let script = """
        tell application id "\(bundleIdentifier)"
            get name
        end tell
        """

        var error: NSDictionary?
        let appleScript = NSAppleScript(source: script)
        _ = appleScript?.executeAndReturnError(&error)

        if let error = error {
            let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
            logger.info(
                "AUTOMATION: NSAppleScript fallback error \(errorCode) for \(appName)",
                service: serviceName
            )
        } else {
            logger.info(
                "AUTOMATION: NSAppleScript fallback succeeded for \(appName)",
                service: serviceName
            )
        }
    }

    /// Trigger automation permission using AppleScript that definitely requires permission
    private func triggerAutomationPermissionWithAppleScript(
        for bundleIdentifier: String, appName: String
    ) {
        logger.info(
            "AUTOMATION: Executing AppleScript that requires automation permission for \(appName)",
            service: serviceName
        )

        // This specific AppleScript pattern is known to trigger native automation permission dialogs
        // It attempts to get application properties, which requires automation permission
        let script = """
            try
                tell application id "\(bundleIdentifier)"
                    get properties
                end tell
            on error errMsg number errNum
                if errNum is -1743 then
                    return "Permission dialog should have appeared"
                else
                    return "Error: " & errNum & " - " & errMsg
                end if
            end try
            """

        var error: NSDictionary?
        let appleScript = NSAppleScript(source: script)
        let result = appleScript?.executeAndReturnError(&error)

        if let error = error {
            let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
            logger.info(
                "AUTOMATION: AppleScript error \(errorCode) for \(appName)",
                service: serviceName
            )

            if errorCode == -1743 {
                logger.info(
                    "AUTOMATION: Error -1743 indicates native permission dialog should have appeared for \(appName)",
                    service: serviceName
                )
            } else if errorCode == -1728 {
                logger.info(
                    "AUTOMATION: Application not found error - trying alternative approach",
                    service: serviceName
                )
                // Try alternative approach with process name
                triggerAutomationPermissionWithProcessName(for: appName)
            } else {
                logger.warning(
                    "AUTOMATION: Unexpected error \(errorCode): \(error["OSAScriptErrorMessage"] as? String ?? "Unknown")",
                    service: serviceName
                )
            }
        } else if let result = result {
            logger.info(
                "AUTOMATION: AppleScript succeeded for \(appName) - Result: \(result.stringValue ?? "nil")",
                service: serviceName
            )
            logger.info(
                "AUTOMATION: Success may indicate permission was already granted or dialog was approved",
                service: serviceName
            )
        }
    }

    /// Alternative approach using process name for automation permission
    private func triggerAutomationPermissionWithProcessName(for appName: String) {
        logger.info(
            "AUTOMATION: Trying alternative approach with process name for \(appName)",
            service: serviceName
        )

        // Alternative AppleScript that uses process name instead of bundle ID
        let script = """
            try
                tell application "\(appName)"
                    activate
                    get name
                end tell
            on error errMsg number errNum
                if errNum is -1743 then
                    return "Permission dialog should have appeared"
                else
                    return "Error: " & errNum & " - " & errMsg
                end if
            end try
            """

        var error: NSDictionary?
        let appleScript = NSAppleScript(source: script)
        _ = appleScript?.executeAndReturnError(&error)

        if let error = error {
            let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
            logger.info(
                "AUTOMATION: Alternative AppleScript error \(errorCode) for \(appName)",
                service: serviceName
            )

            if errorCode == -1743 {
                logger.info(
                    "AUTOMATION: Native automation permission dialog should have appeared for \(appName)",
                    service: serviceName
                )
            }
        } else {
            logger.info(
                "AUTOMATION: Alternative AppleScript succeeded for \(appName)",
                service: serviceName
            )
        }
    }

    /// Show sandboxed-specific automation permission instructions
    private func showSandboxedAutomationInstructions(for appName: String, bundleIdentifier: String)
    {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "Automation Permission Required"
            alert.informativeText = """
                Snapback needs permission to control \(appName).

                Since Snapback is sandboxed, you need to manually enable automation permissions:

                1. Open System Settings > Privacy & Security > Automation
                2. Look for "Snapback" in the list
                3. Enable the toggle next to "\(appName)"

                If Snapback doesn't appear in the Automation list:
                • Restart Snapback and try again
                • The entry should appear after attempting window operations

                Bundle ID: \(bundleIdentifier)
                """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "Open System Settings")
            alert.addButton(withTitle: "OK")

            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                if let url = URL(
                    string:
                        "x-apple.systempreferences:com.apple.preference.security?Privacy_Automation"
                ) {
                    NSWorkspace.shared.open(url)
                }
            }
        }
    }

    /// Show automation permission setup instructions to the user
    private func showAutomationPermissionInstructions(for appName: String, reason: String) {
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "Automation Permission Required"
            alert.informativeText = """
                Snapback needs permission to control \(appName).

                Reason: \(reason)

                To enable window management for \(appName):

                1. Open System Settings > Privacy & Security > Automation
                2. Find "Snapback" in the list
                3. Enable the toggle next to "\(appName)"
                4. Try the window operation again

                If Snapback doesn't appear in Automation settings, restart Snapback and try again.
                """
            alert.alertStyle = .informational
            alert.addButton(withTitle: "Open System Settings")
            alert.addButton(withTitle: "OK")

            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                if let url = URL(
                    string:
                        "x-apple.systempreferences:com.apple.preference.security?Privacy_Automation"
                ) {
                    NSWorkspace.shared.open(url)
                }
            }
        }
    }

    /// Fallback automation permission request using bundle identifier
    private func requestAutomationPermissionFallback(for bundleIdentifier: String, appName: String)
    {
        logger.info(
            "🔍 AUTOMATION DIAGNOSTIC: Using fallback automation permission request for \(bundleIdentifier)",
            service: serviceName
        )

        // Alternative approach: Use Apple Events with bundle identifier directly
        let targetDescriptor = NSAppleEventDescriptor(bundleIdentifier: bundleIdentifier)
        let appleEventDescriptor = NSAppleEventDescriptor(
            eventClass: AEEventClass(kCoreEventClass),
            eventID: AEEventID(kAEGetData),
            targetDescriptor: targetDescriptor,
            returnID: AEReturnID(kAutoGenerateReturnID),
            transactionID: AETransactionID(kAnyTransactionID)
        )

        logger.info(
            "🔍 AUTOMATION DIAGNOSTIC: Sending fallback Apple Event with bundle ID for \(appName)",
            service: serviceName
        )

        do {
            let result = try appleEventDescriptor.sendEvent(options: [], timeout: 10.0)
            logger.info(
                "🔍 AUTOMATION DIAGNOSTIC: Fallback Apple Event succeeded for \(bundleIdentifier) - Result: \(result.stringValue ?? "nil")",
                service: serviceName
            )
        } catch {
            let nsError = error as NSError
            logger.info(
                "🔍 AUTOMATION DIAGNOSTIC: Fallback Apple Event for \(bundleIdentifier) - Error: \(nsError.code) (\(nsError.localizedDescription))",
                service: serviceName
            )

            if nsError.code == -1743 {
                logger.info(
                    "🔍 AUTOMATION DIAGNOSTIC: Native automation permission dialog should have appeared for \(appName) (fallback)",
                    service: serviceName
                )
            } else {
                logger.warning(
                    "🔴 AUTOMATION DIAGNOSTIC: Both primary and fallback automation requests failed for \(appName)",
                    service: serviceName
                )
            }
        }
    }

    /// Check if automation permission is granted for a specific application
    func checkAutomationPermission(for bundleIdentifier: String) -> Bool {
        logger.debug(
            "AUTOMATION: Checking permission for \(bundleIdentifier)",
            service: serviceName
        )

        guard
            let targetApp = NSWorkspace.shared.runningApplications.first(where: {
                $0.bundleIdentifier == bundleIdentifier
            })
        else {
            logger.warning(
                "AUTOMATION: Application \(bundleIdentifier) is not running",
                service: serviceName
            )
            return false
        }

        // Direct accessibility test - this is what we actually need for window management
        let appElement = AXUIElementCreateApplication(targetApp.processIdentifier)
        var titleRef: AnyObject?
        let error = AXUIElementCopyAttributeValue(
            appElement,
            kAXTitleAttribute as CFString,
            &titleRef
        )

        let hasPermission = (error == .success)

        logger.info(
            "AUTOMATION: Permission check for \(bundleIdentifier) - Error: \(error.rawValue), Has Permission: \(hasPermission)",
            service: serviceName
        )

        return hasPermission
    }

    /// Request accessibility permissions
    func requestAccessibilityPermission() {
        let timestamp = Date()
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: requestAccessibilityPermission() called at \(timestamp)",
            service: serviceName)
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Current permission status before request: \(isAccessibilityPermissionGranted)",
            service: serviceName)

        // SANDBOXING FIX: Handle sandboxed vs non-sandboxed apps differently
        if isSandboxed() {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: SANDBOXED - Using system permission prompt only",
                service: serviceName)

            // For sandboxed apps, ONLY use the system dialog - no custom dialog needed
            // This will show the native macOS permission dialog if permissions aren't granted
            let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue() as String: true]
            let currentStatus = AXIsProcessTrustedWithOptions(options as CFDictionary)

            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: SANDBOXED - AXIsProcessTrustedWithOptions with prompt returned: \(currentStatus)",
                service: serviceName)

            // Update our internal state immediately
            if currentStatus != isAccessibilityPermissionGranted {
                logger.info(
                    "🔍 PERMISSION DIAGNOSTIC: SANDBOXED - Permission status changed from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                    service: serviceName)

                isAccessibilityPermissionGranted = currentStatus
                updatePersistentState(currentStatus)

                // Notify observers of the change
                NotificationCenter.default.post(
                    name: PermissionManager.permissionStatusChanged,
                    object: currentStatus
                )
            }

            return  // Exit early - no custom dialog needed for sandboxed apps
        }

        // NON-SANDBOXED: Use custom dialog approach
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: NON-SANDBOXED - Using custom permission dialog",
            service: serviceName)

        let alert = NSAlert()
        alert.messageText = "Accessibility Permissions Required"
        alert.informativeText =
            "Snapback needs Accessibility permissions to manage window positions. Please grant access in System Settings > Privacy & Security > Accessibility."
        alert.alertStyle = .warning
        alert.addButton(withTitle: "Open Settings")
        alert.addButton(withTitle: "Cancel")

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: About to show custom permission dialog", service: serviceName)
        let response = alert.runModal()

        if response == .alertFirstButtonReturn {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Open Settings' button", service: serviceName
            )
            openSystemSettings()
        } else {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: User clicked 'Cancel' button or dismissed dialog",
                service: serviceName)
        }

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Permission dialog interaction complete", service: serviceName)
    }

    /// Open System Settings to the Accessibility privacy section
    private func openSystemSettings() {
        // Try the modern System Settings URL first (macOS 13+)
        if let url = URL(
            string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")
        {
            NSWorkspace.shared.open(url)
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Opened System Settings for accessibility permissions",
                service: serviceName)
        } else {
            logger.error(
                "🔍 PERMISSION DIAGNOSTIC: Failed to create URL for System Settings",
                service: serviceName)
        }
    }

    /// Periodically check permission status with adaptive timing
    @objc private func checkPermissionStatus() {
        // Reduce logging frequency - only log every 10th check when permissions are granted
        let shouldLogDetails = !isAccessibilityPermissionGranted || (permissionCheckCount % 10 == 0)

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Periodic permission check triggered (check #\(permissionCheckCount))",
                service: serviceName)
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Current stored permission status: \(isAccessibilityPermissionGranted)",
                service: serviceName)
        }

        let currentStatus = checkAccessibilityPermission()

        if shouldLogDetails {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Fresh permission check result: \(currentStatus)",
                service: serviceName)
        }

        // If permission status has changed, update and notify
        if currentStatus != isAccessibilityPermissionGranted {
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status CHANGED from \(isAccessibilityPermissionGranted) to \(currentStatus)",
                service: serviceName)

            isAccessibilityPermissionGranted = currentStatus

            // Update persistent state when status changes
            updatePersistentState(currentStatus)

            // Update adaptive timing based on new status
            if currentStatus {
                successfulGrantedChecks = 1  // Start counting successful checks
            } else {
                successfulGrantedChecks = 0  // Reset when denied
            }

            // Restart timer with new interval if status changed
            startAdaptiveTimer()

            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Posting permission status change notification with value: \(isAccessibilityPermissionGranted)",
                service: serviceName)
            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )
            logger.info(
                "🔍 PERMISSION DIAGNOSTIC: Permission status change notification posted",
                service: serviceName)
        } else if currentStatus {
            // Permission is granted and unchanged - increment successful check counter
            successfulGrantedChecks += 1

            // If we've reached the threshold, restart timer with longer interval
            if successfulGrantedChecks == maxChecksWhenGranted {
                logger.info(
                    "🔍 PERMISSION DIAGNOSTIC: Permissions stable after \(maxChecksWhenGranted) checks - switching to longer interval",
                    service: serviceName)
                startAdaptiveTimer()
            }
        }

        // Only log unchanged status occasionally to reduce noise
        if shouldLogDetails && currentStatus == isAccessibilityPermissionGranted {
            logger.debug(
                "🔍 PERMISSION DIAGNOSTIC: Permission status unchanged (\(currentStatus))",
                service: serviceName)
        }
    }

    /// Manually trigger a permission check (useful when app becomes active)
    func triggerPermissionCheck() {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Manual permission check triggered", service: serviceName)
        checkPermissionStatus()
    }

    /// Get current timer interval for debugging
    func getCurrentTimerInterval() -> TimeInterval? {
        return permissionCheckTimer?.timeInterval
    }

    /// Clear persistent permission state (useful for testing)
    func clearPersistentState() {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Clearing persistent permission state",
            service: serviceName)

        UserDefaults.standard.removeObject(forKey: PersistenceKeys.hasEverBeenGranted)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastKnownStatus)
        UserDefaults.standard.removeObject(forKey: PersistenceKeys.lastCheckTimestamp)
        UserDefaults.standard.synchronize()

        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Persistent permission state cleared",
            service: serviceName)
    }

    /// Get persistent state for debugging
    func getPersistentState() -> (
        hasEverBeenGranted: Bool, lastKnownStatus: Bool, lastCheckTimestamp: TimeInterval
    ) {
        return (
            hasEverBeenGranted: UserDefaults.standard.bool(
                forKey: PersistenceKeys.hasEverBeenGranted),
            lastKnownStatus: UserDefaults.standard.bool(forKey: PersistenceKeys.lastKnownStatus),
            lastCheckTimestamp: UserDefaults.standard.double(
                forKey: PersistenceKeys.lastCheckTimestamp)
        )
    }

    /// Force a fresh permission check (useful for testing and debugging)
    func forceFreshPermissionCheck() -> Bool {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: Force fresh permission check requested",
            service: serviceName)

        let previousState = isAccessibilityPermissionGranted
        let freshResult = checkAccessibilityPermission()

        if freshResult != previousState {
            logger.warning(
                "🔍 PERMISSION DIAGNOSTIC: Force check detected state change from \(previousState) to \(freshResult)",
                service: serviceName)

            // Update our state and notify
            isAccessibilityPermissionGranted = freshResult

            // Post notification about permission change
            NotificationCenter.default.post(
                name: PermissionManager.permissionStatusChanged,
                object: isAccessibilityPermissionGranted
            )

            // Restart timer with appropriate interval
            startAdaptiveTimer()
        }

        return freshResult
    }

    deinit {
        logger.info(
            "🔍 PERMISSION DIAGNOSTIC: PermissionManager deinitializing", service: serviceName)
        permissionCheckTimer?.invalidate()
    }
}
