<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Basic Sandbox -->
    <key>com.apple.security.app-sandbox</key>
    <true/>
    
    <!-- Permissions -->
    <key>com.apple.security.accessibility</key>
    <true/>
    <key>com.apple.security.device.screen</key>
    <true/>
    
    <!-- Network -->
    <key>com.apple.security.network.client</key>
    <true/>
    
    <!-- Files -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    
    <!-- Window Server -->
    <key>com.apple.security.temporary-exception.mach-lookup.global-name</key>
    <array>
        <string>com.apple.universalaccessAuthWarn</string>
        <string>com.apple.universalaccessd</string>
        <string>com.apple.windowserver.active</string>
    </array>

    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>
    </dict>
</plist>
