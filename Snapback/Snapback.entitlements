<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Enable App Sandbox -->
    <key>com.apple.security.app-sandbox</key>
    <true/>

    <!-- Accessibility permissions for window management -->
    <key>com.apple.security.accessibility</key>
    <true/>

    <!-- Screen recording for window detection and manipulation -->
    <key>com.apple.security.device.screen</key>
    <true/>

    <!-- Allow outgoing network connections for CloudKit sync -->
    <key>com.apple.security.network.client</key>
    <true/>

    <!-- Allow reading/writing user-selected files for import/export -->
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>

    <!-- Disable library validation for accessibility APIs -->
    <key>com.apple.security.cs.disable-library-validation</key>
    <true/>

    <!-- Allow access to system services for window management -->
    <key>com.apple.security.temporary-exception.mach-lookup.global-name</key>
    <array>
        <string>com.apple.universalaccessAuthWarn</string>
        <string>com.apple.universalaccessd</string>
        <string>com.apple.windowserver.active</string>
    </array>

    
</dict>
</plist>
