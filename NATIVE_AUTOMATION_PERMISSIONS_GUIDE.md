# Native Automation Permissions Fix for Sandboxed Snapback

## 🎯 **Problem Solved**

The custom dialog approach wasn't triggering native macOS automation permission prompts. Snapback wasn't appearing in System Settings > Automation because we weren't using the proper native APIs.

## ✅ **Native Permission Solution**

### **1. AppleScript-Based Native Prompts**
- **Uses `NSAppleScript`**: Executes AppleScript commands that trigger native permission dialogs
- **Automatic Registration**: Native prompts automatically add Snapback to System Settings > Automation
- **Standard macOS Experience**: Users see familiar system permission dialogs

### **2. Improved Permission Flow**
```swift
// Native permission request using AppleScript
let script = """
tell application "\(appName)"
    try
        get name
    on error
        return "permission_requested"
    end try
end tell
"""
```

### **3. Enhanced Error Handling**
- **Error -1743**: Automation permission denied/not granted (triggers native dialog)
- **Error -1728**: Application not found (uses bundle identifier fallback)
- **Error -600**: Process not found (handled gracefully)

## 🧪 **Testing Protocol**

### **Step 1: Clean State**
1. **Remove existing automation permissions**:
   - System Settings > Privacy & Security > Automation
   - Remove Snapback if present
2. **Restart Snapback**

### **Step 2: Test Native Permission Flow**
1. **Try window snapping** with VS Code (or any app)
2. **Expected behavior**:
   - Native macOS automation permission dialog appears
   - Dialog shows: "Snapback would like to control Visual Studio Code"
   - Click "OK" to grant permission
3. **Verify in System Settings**:
   - Snapback should now appear in Automation settings
   - VS Code toggle should be enabled under Snapback

### **Step 3: Verify Functionality**
1. **After granting permission**:
   - Window snapping should work immediately
   - No more `kAXErrorCannotComplete (-25204)` errors
   - Logs should show successful accessibility API calls

## 🔍 **Expected Log Messages**

### **✅ Native Permission Request:**
```
🔍 AUTOMATION DIAGNOSTIC: Requesting native automation permission for Visual Studio Code (com.microsoft.VSCode)
🔍 AUTOMATION DIAGNOSTIC: AppleScript execution for Visual Studio Code - Error: -1743
🔍 AUTOMATION DIAGNOSTIC: Automation permission dialog should have appeared for Visual Studio Code
```

### **✅ Permission Granted:**
```
🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: success (0), Permission: true
🔍 AUTOMATION DIAGNOSTIC: Permission status after native request for Visual Studio Code: true
✅ SANDBOX DIAGNOSTIC: Successfully got focused window
```

### **❌ Permission Denied:**
```
🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: apiDisabled (-25200), Permission: false
🔴 SANDBOX DIAGNOSTIC: Automation permission still not granted for Visual Studio Code
```

## 🚨 **Troubleshooting**

### **Issue: Native dialog doesn't appear**
**Solutions:**
1. Ensure VS Code is actually running
2. Check Console.app for system-level errors
3. Try with a simpler app (TextEdit, Calculator)
4. Verify `com.apple.security.automation.apple-events` entitlement

### **Issue: Dialog appears but permission still fails**
**Solutions:**
1. Make sure you clicked "OK" in the dialog (not "Don't Allow")
2. Check System Settings > Automation to verify toggle is enabled
3. Restart Snapback after granting permission
4. Try disabling and re-enabling the toggle

### **Issue: Error -1728 (Application not found)**
**Solutions:**
1. Check that the app name matches exactly (case-sensitive)
2. Fallback will try bundle identifier approach
3. Some apps have different display names vs AppleScript names

### **Issue: Multiple permission dialogs**
**Solutions:**
1. This is normal - each app needs its own permission
2. Grant permission for each app you want to control
3. Permissions persist across Snapback restarts

## 📋 **Key Differences from Custom Dialog Approach**

### **❌ Old Custom Dialog Approach:**
- Showed Snapback's own dialog
- Manually redirected to System Settings
- Required manual setup by user
- Snapback didn't appear in Automation settings automatically

### **✅ New Native Prompt Approach:**
- Shows standard macOS system dialog
- Automatically registers Snapback in System Settings
- One-click permission granting
- Follows macOS permission patterns

## 🎉 **Expected Results**

After implementing native automation permissions:

1. **✅ Native Permission Experience**: Standard macOS automation dialogs
2. **✅ Automatic Registration**: Snapback appears in System Settings > Automation
3. **✅ One-Click Setup**: Users just click "OK" in native dialog
4. **✅ Immediate Functionality**: Window management works right after permission grant
5. **✅ Persistent Permissions**: Settings survive app restarts
6. **✅ No More AXError -25204**: Accessibility APIs work properly

## 🔗 **Files Modified**

- `PermissionManager.swift` - Native AppleScript-based permission requests
- `WindowSnappingService.swift` - Improved permission checking and re-verification
- Removed custom dialog methods in favor of native system prompts

## 🎯 **Key Insight**

The critical difference is using **AppleScript execution** to trigger native macOS automation permission dialogs, rather than custom app dialogs. This ensures proper system integration and automatic registration in System Settings.

**Native AppleScript approach = Proper macOS automation permission flow**
