import Foundation
import AppKit

/// Simple, working automation permission request implementation
extension PermissionManager {
    
    /// Request automation permission using the most reliable method
    func requestAutomationPermissionSimple(for bundleIdentifier: String, appName: String) {
        logger.info(
            "AUTOMATION: Requesting permission for \(appName) (\(bundleIdentifier))",
            service: serviceName
        )
        
        // Method 1: Direct AppleScript execution
        let script = "tell application \"\(appName)\" to get name"
        
        var error: NSDictionary?
        let appleScript = NSAppleScript(source: script)
        let result = appleScript?.executeAndReturnError(&error)
        
        if let error = error {
            let errorCode = error["OSAScriptErrorNumber"] as? Int ?? 0
            logger.info(
                "AUTOMATION: AppleScript error \(errorCode) for \(appName) - this may trigger permission dialog",
                service: serviceName
            )
            
            // Error -1743 means permission dialog should appear
            if errorCode == -1743 {
                logger.info(
                    "AUTOMATION: Permission dialog should have appeared for \(appName)",
                    service: serviceName
                )
            }
        } else {
            logger.info(
                "AUTOMATION: AppleScript succeeded for \(appName) - permission already granted",
                service: serviceName
            )
        }
        
        // Method 2: Fallback with bundle identifier
        if error != nil {
            let fallbackScript = "tell application id \"\(bundleIdentifier)\" to get name"
            
            var fallbackError: NSDictionary?
            let fallbackAppleScript = NSAppleScript(source: fallbackScript)
            let fallbackResult = fallbackAppleScript?.executeAndReturnError(&fallbackError)
            
            if let fallbackError = fallbackError {
                let fallbackErrorCode = fallbackError["OSAScriptErrorNumber"] as? Int ?? 0
                logger.info(
                    "AUTOMATION: Fallback AppleScript error \(fallbackErrorCode) for \(bundleIdentifier)",
                    service: serviceName
                )
            } else {
                logger.info(
                    "AUTOMATION: Fallback AppleScript succeeded for \(bundleIdentifier)",
                    service: serviceName
                )
            }
        }
    }
    
    /// Check automation permission using direct accessibility test
    func checkAutomationPermissionSimple(for bundleIdentifier: String) -> Bool {
        guard let targetApp = NSWorkspace.shared.runningApplications.first(where: {
            $0.bundleIdentifier == bundleIdentifier
        }) else {
            return false
        }
        
        let appElement = AXUIElementCreateApplication(targetApp.processIdentifier)
        var titleRef: AnyObject?
        let error = AXUIElementCopyAttributeValue(
            appElement,
            kAXTitleAttribute as CFString,
            &titleRef
        )
        
        let hasPermission = (error == .success)
        
        logger.info(
            "AUTOMATION: Permission check for \(bundleIdentifier) - Error: \(error.rawValue), Has Permission: \(hasPermission)",
            service: serviceName
        )
        
        return hasPermission
    }
}

/*
USAGE INSTRUCTIONS:

1. Replace the corrupted requestAutomationPermission method with:
   requestAutomationPermissionSimple(for:appName:)

2. Replace the checkAutomationPermission method with:
   checkAutomationPermissionSimple(for:)

3. Update WindowSnappingService to use these new methods

This implementation:
- Uses simple AppleScript execution to trigger native permission dialogs
- Has fallback with bundle identifier
- Tests actual accessibility API access for permission checking
- Avoids complex Apple Events that may not work in sandboxed environments
- Provides clear logging for debugging

The key insight is that AppleScript "tell application" commands will trigger
the native automation permission dialog when executed from a sandboxed app.
*/
