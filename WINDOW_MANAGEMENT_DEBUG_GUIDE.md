# Window Management Debugging Guide for Sandboxed Snapback

## 🔧 **Entitlements Fixed**

### **Key Changes Made**
1. **Added Screen Recording Entitlement**: `com.apple.security.device.screen` - Required for window detection
2. **Enhanced Universal Access**: Added `com.apple.universalaccessAuthWarn` and `com.apple.universalaccessd` services
3. **Disabled Library Validation**: `com.apple.security.cs.disable-library-validation` - Allows accessibility API access
4. **Consistent Entitlements**: Both debug and release entitlements files now match

### **Critical Entitlements for Window Management**
```xml
<!-- Screen recording for window detection and manipulation -->
<key>com.apple.security.device.screen</key>
<true/>

<!-- Accessibility permissions for window management -->
<key>com.apple.security.accessibility</key>
<true/>

<!-- Disable library validation for accessibility APIs -->
<key>com.apple.security.cs.disable-library-validation</key>
<true/>

<!-- Allow access to system services for window management -->
<key>com.apple.security.temporary-exception.mach-lookup.global-name</key>
<array>
    <string>com.apple.universalaccessAuthWarn</string>
    <string>com.apple.universalaccessd</string>
    <string>com.apple.windowserver.active</string>
</array>
```

## 🧪 **Testing Steps**

### **Step 1: Clean Build with New Entitlements**
```bash
# Clean everything to ensure entitlements are applied
xcodebuild clean -project Snapback.xcodeproj
rm -rf ~/Library/Developer/Xcode/DerivedData/Snapback-*

# Build with new entitlements
xcodebuild build -project Snapback.xcodeproj -configuration Debug
```

### **Step 2: Test Permission Flow**
1. **Remove all existing permissions**:
   - System Settings > Privacy & Security > Accessibility (remove Snapback)
   - System Settings > Privacy & Security > Screen Recording (remove Snapback if present)

2. **Launch Snapback**:
   - Should show system accessibility permission dialog
   - Grant accessibility permission
   - May also prompt for screen recording permission

3. **Verify permissions granted**:
   - Check both Accessibility and Screen Recording sections in System Settings
   - Both should show Snapback with toggles enabled

### **Step 3: Test Window Management**
1. **Open a test application** (e.g., TextEdit, Calculator)
2. **Try basic window snapping**:
   - Use keyboard shortcuts to snap windows
   - Try drag-to-snap if enabled
3. **Monitor logs** for diagnostic messages

## 🔍 **Diagnostic Logging**

### **Key Log Messages to Watch For**

**✅ Success Indicators:**
```
✅ SANDBOX DIAGNOSTIC: Successfully set kAXPositionAttribute to (x, y)
✅ SANDBOX DIAGNOSTIC: Successfully set kAXSizeAttribute to (width, height)
🔍 PERMISSION DIAGNOSTIC: Permissions already granted - skipping permission request
```

**🔴 Error Indicators:**
```
🔴 SANDBOX DIAGNOSTIC: API_DISABLED error - This suggests missing entitlements
🔴 SANDBOX DIAGNOSTIC: NOT_IMPLEMENTED error - Target app doesn't support accessibility
🔴 SANDBOX DIAGNOSTIC: AXUIElementSetAttributeValue failed with error: [error_code]
```

### **Common Error Codes and Solutions**

| Error Code | Meaning | Solution |
|------------|---------|----------|
| `-25200` (kAXErrorAPIDisabled) | Accessibility API disabled | Check entitlements and permissions |
| `-25201` (kAXErrorNoValue) | Attribute has no value | Normal for some attributes |
| `-25202` (kAXErrorIllegalArgument) | Invalid argument | Check coordinate system |
| `-25203` (kAXErrorInvalidUIElement) | Invalid window element | Window may have closed |
| `-25204` (kAXErrorCannotComplete) | Operation cannot complete | App may not support resizing |

## 🚨 **Troubleshooting**

### **Issue: Window Management Still Not Working**

**Check 1: Verify Entitlements Applied**
```bash
# Check if entitlements are properly embedded
codesign -d --entitlements - /path/to/Snapback.app
```

**Check 2: Test with Non-Sandboxed Build**
- Temporarily remove `com.apple.security.app-sandbox` from entitlements
- Test if window management works without sandbox
- If it works, the issue is sandbox-specific

**Check 3: Test with Different Applications**
- Some apps (like System Preferences) may not allow window manipulation
- Test with simple apps like TextEdit, Calculator, or Finder windows

**Check 4: Check System Integrity Protection (SIP)**
```bash
# Check if SIP is interfering
csrutil status
```

### **Issue: Screen Recording Permission Not Requested**

If screen recording permission isn't requested automatically:
1. Manually add Snapback to Screen Recording permissions
2. Check if `com.apple.security.device.screen` entitlement is properly applied
3. Restart the app after granting permissions

### **Issue: Accessibility API Errors**

**For API_DISABLED errors:**
1. Verify accessibility permissions are granted
2. Check that `com.apple.security.cs.disable-library-validation` is present
3. Ensure universal access services are in mach-lookup exceptions

**For coordinate system issues:**
1. Check if coordinate flipping is working correctly
2. Verify screen detection logic
3. Test on different display configurations

## 📝 **Next Steps**

1. **Test thoroughly** with the updated entitlements
2. **Monitor logs** during window management operations
3. **Test on different macOS versions** (if possible)
4. **Test with various applications** to ensure compatibility
5. **Consider App Store submission** once functionality is verified

## 🔗 **Files Modified**

- `Snapback/Snapback.entitlements` - Added comprehensive sandbox entitlements
- `Snapback/SnapbackRelease.entitlements` - Synchronized with debug entitlements
- `Snapback/Features/Core/AccessibilityElement.swift` - Enhanced diagnostic logging

## 📋 **Verification Checklist**

- [ ] Clean build completed successfully
- [ ] Both accessibility and screen recording permissions granted
- [ ] No permission dialogs on subsequent launches
- [ ] Window snapping works with test applications
- [ ] Drag-to-snap functionality works (if enabled)
- [ ] Workspace save/restore functions properly
- [ ] No API_DISABLED errors in logs
- [ ] Coordinate system working correctly across displays
