# Snapback Sandboxing Fix Summary

## 🔧 **Issues Fixed**

### **1. Malformed Entitlements File**

- **Problem**: `SnapbackRelease.entitlements` had invalid XML with an extra empty `<dict/>` tag
- **Fix**: Corrected XML structure and added comprehensive entitlements

### **2. Missing Sandbox Entitlement**

- **Problem**: Neither entitlements file contained `com.apple.security.app-sandbox`
- **Fix**: Added sandbox entitlement to both files

### **3. Dual Permission Dialogs Issue**

- **Problem**: Both system dialog and custom dialog were appearing, causing confusion
- **Fix**: Sandboxed apps now use ONLY the system permission dialog

### **4. Repeated Permission Prompts**

- **Problem**: System dialog kept appearing even after permissions were granted
- **Fix**: Separated routine permission checking from permission requesting logic

### **5. Incorrect Permission API Usage**

- **Problem**: Used `AXIsProcessTrustedWithOptions()` with prompt for routine checks
- **Fix**: Only use prompt option when explicitly requesting permissions, not for status checks

## 📋 **Entitlements Added**

Both `Snapback.entitlements` and `SnapbackRelease.entitlements` now include:

```xml
<!-- Enable App Sandbox -->
<key>com.apple.security.app-sandbox</key>
<true/>

<!-- Accessibility permissions for window management -->
<key>com.apple.security.accessibility</key>
<true/>

<!-- Allow outgoing network connections for CloudKit sync -->
<key>com.apple.security.network.client</key>
<true/>

<!-- Allow reading/writing user-selected files for import/export -->
<key>com.apple.security.files.user-selected.read-write</key>
<true/>

<!-- Allow access to window server for workspace management -->
<key>com.apple.security.temporary-exception.mach-lookup.global-name</key>
<array>
    <string>com.apple.windowserver.active</string>
</array>

<!-- CloudKit entitlement for workspace sync -->
<key>com.apple.developer.icloud-container-identifiers</key>
<array>
    <string>iCloud.$(CFBundleIdentifier)</string>
</array>

<!-- CloudKit services -->
<key>com.apple.developer.icloud-services</key>
<array>
    <string>CloudKit</string>
</array>
```

## 🔄 **Code Changes**

### **PermissionManager.swift Updates**

1. **Fixed Permission Checking Logic**:

   - `checkAccessibilityPermission()` now ALWAYS uses `AXIsProcessTrusted()` for routine checks
   - Removed prompt option from routine permission status checks to prevent repeated dialogs
   - Added `isSandboxed()` method to detect sandbox environment

2. **Streamlined Permission Requesting**:
   - **Sandboxed apps**: Use ONLY `AXIsProcessTrustedWithOptions()` with prompt (system dialog)
   - **Non-sandboxed apps**: Use custom NSAlert dialog with manual settings navigation
   - Eliminated dual dialog issue by using different flows for different environments
   - Added immediate state updates when permissions change

### **AppDelegate.swift Updates**

1. **Enhanced Permission Flow**:
   - Added `forceFreshPermissionCheck()` call before making permission decisions
   - Ensures most current permission status before showing any dialogs
   - Prevents unnecessary permission requests when permissions are already granted

## 🧪 **Testing Instructions**

### **1. Build and Test the Sandboxed App**

```bash
# Clean build to ensure entitlements are applied
xcodebuild clean -project Snapback.xcodeproj
xcodebuild build -project Snapback.xcodeproj -configuration Release
```

### **2. Test Permission Flow (CRITICAL)**

**Step 1: Clean Permission State**

1. Remove existing permissions:
   - Open System Settings > Privacy & Security > Accessibility
   - Remove Snapback from the list if present
2. Quit Snapback completely if running

**Step 2: Test First Launch**

1. Launch Snapback
2. **Expected behavior**:
   - ONLY the system permission dialog should appear (native macOS dialog)
   - NO custom "Accessibility Permissions Required" dialog should appear
3. Click "Open System Settings" in the system dialog
4. Verify Snapback appears in System Settings > Privacy & Security > Accessibility
5. Enable the toggle for Snapback

**Step 3: Test Subsequent Launches**

1. Quit and relaunch Snapback
2. **Expected behavior**:
   - NO permission dialogs should appear
   - App should start normally with full functionality
3. Check logs for: `"Permissions already granted - skipping permission request"`

**Step 4: Test Permission Revocation**

1. With Snapback running, disable its toggle in System Settings
2. **Expected behavior**:
   - App should detect the change within 30 seconds
   - Menu should switch to limited functionality
3. Re-enable the toggle
4. **Expected behavior**:
   - App should detect the change and restore full functionality

### **3. Test Core Functionality**

After granting permissions, verify:

- ✅ Window management features work
- ✅ Workspace save/restore functions
- ✅ CloudKit sync operates (if configured)
- ✅ Import/export functionality works
- ✅ All keyboard shortcuts respond

### **4. Monitor Logs**

Watch for these diagnostic messages:

```
🔍 PERMISSION DIAGNOSTIC: SANDBOXED - AXIsProcessTrustedWithOptions() returned: [true/false]
🔍 PERMISSION DIAGNOSTIC: FIRST CHECK ON APP LAUNCH - Permission status = [true/false], Sandboxed = true
```

## 🚨 **Potential Issues & Solutions**

### **Issue**: App crashes on launch

**Solution**: Check that all entitlements are properly formatted and no typos exist

### **Issue**: Permission toggle still doesn't appear

**Solution**:

1. Verify the app is actually sandboxed (check logs for "Sandboxed = true")
2. Ensure you're testing with a clean build
3. Try manually adding the app to Accessibility settings

### **Issue**: CloudKit sync fails

**Solution**: Verify your Apple Developer account has CloudKit enabled and container is configured

### **Issue**: Window management doesn't work after granting permissions

**Solution**: Check for additional entitlements that might be needed for specific window server APIs

## 🔍 **Verification Checklist**

- [ ] App builds without errors
- [ ] Entitlements files are valid XML
- [ ] System permission dialog appears on first launch
- [ ] Snapback appears in System Settings > Privacy & Security > Accessibility
- [ ] Permission toggle is functional
- [ ] Core window management works after granting permissions
- [ ] Workspace save/restore functions properly
- [ ] No crashes or unexpected behavior
- [ ] Logs show "Sandboxed = true" on launch

## 📝 **Next Steps**

1. **Test thoroughly** with the updated entitlements
2. **Monitor user feedback** for any remaining permission issues
3. **Consider App Store submission** once all functionality is verified
4. **Update documentation** to reflect sandboxing requirements

## 🔗 **Related Files Modified**

- `Snapback/Snapback.entitlements`
- `Snapback/SnapbackRelease.entitlements`
- `Snapback/Features/Permissions/PermissionManager.swift`
