# Automation Permission Debugging Guide

## 🚨 **Current Issue Analysis**

You're experiencing a **false positive** in automation permission checking. The `checkAutomationPermission()` method returns `true`, but accessibility APIs still fail with `kAXErrorCannotComplete (-25204)`.

## 🔍 **Root Cause**

The original automation permission check was using Apple Events, which can succeed even when accessibility-based automation is blocked. The new implementation directly tests accessibility API access, which is what <PERSON><PERSON><PERSON> actually needs.

## 🛠️ **Enhanced Diagnostic Features**

### **1. Improved Permission Checking**
- **Direct Accessibility Test**: Now tests `kAXTitleAttribute` access instead of Apple Events
- **Detailed Logging**: Shows exact error codes and permission states
- **False Positive Detection**: Identifies when Apple Events work but accessibility doesn't

### **2. Better User Guidance**
- **Clear Instructions**: Step-by-step automation permission setup
- **System Settings Integration**: Direct links to automation settings
- **Manual Setup Support**: Guidance when automatic permission requests fail

## 🧪 **Step-by-Step Testing Protocol**

### **Step 1: Verify Current State**
1. **Check System Settings**:
   - Open System Settings > Privacy & Security > Automation
   - Look for "Snapback" in the list
   - Check if VS Code (or target app) has a toggle under Snapback
   - **CRITICAL**: Verify the toggle is actually enabled

2. **Check Logs**:
   - Look for: `🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode`
   - The error code should tell you the real permission state

### **Step 2: Clean Test**
1. **Remove Snapback from Automation settings** (if present)
2. **Restart Snapback**
3. **Try window snapping with VS Code**
4. **Expected behavior**:
   - Should show automation permission dialog
   - Should guide you to System Settings
   - Should show detailed error logging

### **Step 3: Manual Permission Setup**
1. **Open System Settings > Privacy & Security > Automation**
2. **Look for Snapback** in the left sidebar
3. **If Snapback is missing**:
   - Restart Snapback
   - Try window operation again
   - Check if Snapback appears
4. **If Snapback is present**:
   - Enable the toggle for VS Code
   - Try window operation again

### **Step 4: Verify Fix**
1. **Check logs for**:
   ```
   ✅ AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: success (0), Permission: true
   ✅ SANDBOX DIAGNOSTIC: Successfully got focused window
   ```

## 🔍 **Diagnostic Log Analysis**

### **What to Look For:**

**❌ False Positive (Old Behavior):**
```
🔍 AUTOMATION DIAGNOSTIC: Automation permission for VS Code: true
❌ Failed to get focused window: AXError(rawValue: -25204)
```

**✅ Correct Detection (New Behavior):**
```
🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: apiDisabled (-25200), Permission: false
🔴 AUTOMATION DIAGNOSTIC: No automation permission for VS Code - requesting permission
```

**✅ Working State:**
```
🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: success (0), Permission: true
✅ SANDBOX DIAGNOSTIC: Successfully got focused window
```

## 🚨 **Common Issues & Solutions**

### **Issue 1: Snapback doesn't appear in Automation settings**
**Solutions:**
1. Restart Snapback completely
2. Try triggering automation request again
3. Check that `com.apple.security.automation.apple-events` entitlement is present
4. Manually add Snapback if needed

### **Issue 2: Toggle is enabled but still getting errors**
**Solutions:**
1. Disable and re-enable the toggle
2. Restart both Snapback and the target application
3. Check for multiple Snapback entries in Automation settings
4. Verify you're testing with the correct app bundle identifier

### **Issue 3: Permission dialog never appears**
**Solutions:**
1. Check Console.app for system-level permission errors
2. Verify the target application is actually running
3. Try with a different application (TextEdit, Calculator)
4. Check if SIP (System Integrity Protection) is interfering

### **Issue 4: VS Code specific issues**
**Solutions:**
1. VS Code has multiple bundle identifiers - check logs for exact ID
2. Try with VS Code Insiders vs regular VS Code
3. Some Electron apps have special permission requirements
4. Test with a simpler app first (TextEdit, Calculator)

## 📋 **Manual Verification Checklist**

- [ ] Snapback appears in System Settings > Privacy & Security > Automation
- [ ] Target application toggle is enabled under Snapback
- [ ] Accessibility permission is granted for Snapback
- [ ] Screen recording permission is granted for Snapback
- [ ] Target application is actually running
- [ ] Logs show `Permission: true` for accessibility check
- [ ] No `kAXErrorCannotComplete (-25204)` errors in logs

## 🎯 **Expected Results After Fix**

**Before (False Positive):**
```
🔍 AUTOMATION DIAGNOSTIC: Automation permission for VS Code: true
❌ Failed to get focused window: AXError(rawValue: -25204)
```

**After (Correct Detection):**
```
🔍 AUTOMATION DIAGNOSTIC: Accessibility check for com.microsoft.VSCode - Error: success (0), Permission: true
✅ SANDBOX DIAGNOSTIC: Successfully got focused window
✅ Window snapping successful
```

## 🔗 **Key Files Modified**

- `PermissionManager.swift` - Enhanced automation permission checking with direct accessibility tests
- `WindowSnappingService.swift` - Improved error handling and user feedback
- Added comprehensive user guidance dialogs

The key insight is that **Apple Events automation ≠ Accessibility automation**. Snapback needs the latter, which requires explicit per-app permissions in System Settings.
