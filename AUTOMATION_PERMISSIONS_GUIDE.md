# Automation Permissions Fix for Sandboxed Snapback

## 🎯 **Root Cause Identified**

The `kAXErrorCannotComplete (-25204)` errors were occurring because **sandboxed apps require per-application automation permissions** to access accessibility information from other applications. This is separate from the global accessibility permission.

## 🔧 **Solution Implemented**

### **1. Added Automation Permission Management**
- **New Methods**: `requestAutomationPermission()` and `checkAutomationPermission()`
- **Apple Events Integration**: Uses Apple Events to trigger automation permission dialogs
- **Per-App Permissions**: Handles automation permissions for each target application individually

### **2. Enhanced Window Detection Flow**
- **Automatic Detection**: Checks automation permissions before attempting window access
- **Permission Requests**: Automatically requests automation permissions when needed
- **Comprehensive Logging**: Detailed diagnostic messages for automation permission flow

## 🧪 **Testing Instructions**

### **Step 1: Clean Permission State**
1. **Remove all existing permissions**:
   - System Settings > Privacy & Security > Accessibility (remove Snapback)
   - System Settings > Privacy & Security > Screen Recording (remove Snapback)
   - System Settings > Privacy & Security > Automation (remove all Snapback entries)

### **Step 2: Test Permission Flow**
1. **Launch Snapback**:
   - Should show accessibility permission dialog → Grant it
   - May show screen recording permission dialog → Grant it

2. **Test Window Management**:
   - Open a test application (e.g., TextEdit, Calculator)
   - Try to snap a window using Snapback
   - **Expected**: Automation permission dialog should appear for the target app

### **Step 3: Verify Automation Permissions**
1. **Check System Settings**:
   - Go to System Settings > Privacy & Security > Automation
   - Should see Snapback with toggles for each application you've tried to control
   - Enable the toggles for applications you want Snapback to control

### **Step 4: Test Full Functionality**
1. **After granting automation permissions**:
   - Window snapping should work normally
   - No more `kAXErrorCannotComplete (-25204)` errors
   - Workspace save/restore should function properly

## 🔍 **Expected Log Messages**

### **✅ Success Indicators:**
```
🔍 AUTOMATION DIAGNOSTIC: Automation permission for TextEdit: true
✅ SANDBOX DIAGNOSTIC: Successfully got focused window
✅ Successfully snapped window to left
```

### **🔴 Permission Request Indicators:**
```
🔍 AUTOMATION DIAGNOSTIC: Automation permission for Xcode: false
🔍 AUTOMATION DIAGNOSTIC: Requesting automation permission for Xcode
🔍 AUTOMATION DIAGNOSTIC: Apple Event sent to Xcode - Error (expected): [error details]
```

### **⚠️ Expected Behavior:**
- First attempt to control an app will trigger automation permission dialog
- Subsequent attempts should work without dialogs (if permission granted)
- Each application requires its own automation permission

## 🚨 **Troubleshooting**

### **Issue: Automation permission dialog doesn't appear**
**Solutions:**
1. Ensure the target application is running
2. Check that `com.apple.security.automation.apple-events` entitlement is present
3. Try manually adding Snapback to Automation settings

### **Issue: Still getting kAXErrorCannotComplete**
**Solutions:**
1. Verify automation permissions are granted in System Settings
2. Check that both accessibility AND automation permissions are enabled
3. Restart Snapback after granting permissions

### **Issue: Permission dialog appears repeatedly**
**Solutions:**
1. Grant the automation permission when prompted
2. Check System Settings to ensure the toggle is enabled
3. Restart the target application if needed

## 📋 **Permission Requirements Summary**

For sandboxed Snapback to work properly, you need:

1. **✅ Accessibility Permission** (global)
   - System Settings > Privacy & Security > Accessibility > Snapback ✓

2. **✅ Screen Recording Permission** (global)
   - System Settings > Privacy & Security > Screen Recording > Snapback ✓

3. **✅ Automation Permissions** (per-application)
   - System Settings > Privacy & Security > Automation > Snapback
   - Enable toggles for each app you want to control (TextEdit, Xcode, etc.)

## 🔗 **Files Modified**

- `Snapback/Features/Permissions/PermissionManager.swift` - Added automation permission methods
- `Snapback/Features/WindowManagement/WindowSnappingService.swift` - Added automation permission checks
- Both entitlements files already contain `com.apple.security.automation.apple-events`

## 🎉 **Expected Outcome**

After implementing this fix:
- ✅ No more `kAXErrorCannotComplete (-25204)` errors
- ✅ Window management works with all applications (after granting permissions)
- ✅ Clear permission flow with appropriate user prompts
- ✅ Comprehensive diagnostic logging for troubleshooting

The key insight is that sandboxed apps need **three types of permissions**:
1. Global accessibility permission
2. Global screen recording permission  
3. Per-application automation permissions

This is why the accessibility APIs were failing - the automation permissions were missing!
